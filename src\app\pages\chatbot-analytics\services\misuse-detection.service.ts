import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { ChatMessage, Conversation } from '../models/chat-analytics.model';

export interface MisuseMetrics {
  userId: string;
  userName: string;
  email?: string;
  metrics: {
    // Message Frequency
    messagesPerMinute: number;
    messagesPerHour: number;
    isHighFrequency: boolean;
    highFrequencyThreshold: number;
    highFrequencyWindow: number; // in minutes
    
    // Duplicate Messages
    duplicateMessageRatio: number;
    totalMessages: number;
    duplicateMessages: number;
    isHighDuplicate: boolean;
    duplicateThreshold: number; // percentage (0-1)
    
    // Session Analysis
    sessionCount: number;
    avgSessionLength: number; // in messages
    isSuspiciousSession: boolean;
    minSessionLength: number; // threshold
    
    // Intent Analysis
    fallbackRate: number;
    avgConfidence: number;
    isHighFallback: boolean;
    fallbackThreshold: number; // percentage (0-1)
    
    // Engagement
    avgInputLength: number;
    avgResponseLength: number;
    engagementScore: number; // ratio of response length to input length
    
    // Overall Risk Score (0-100)
    riskScore: number;
    riskLevel: 'low' | 'medium' | 'high';
  };
  lastUpdated: Date;
  flaggedIssues: string[];
}

@Injectable({
  providedIn: 'root'
})
export class MisuseDetectionService {
  constructor() {
  }
  private readonly defaultThresholds = {
    highFrequency: 10, // messages per minute
    highFrequencyWindow: 1, // minutes
    duplicateRatio: 0.5, // 50%
    minSessionLength: 2, // messages
    fallbackRate: 0.3, // 30%
  };

  private readonly metricsSubject = new BehaviorSubject<MisuseMetrics[]>([]);
  metrics$ = this.metricsSubject.asObservable();

  /**
   * Analyze conversations for potential misuse
   */
  analyzeConversations(conversations: Conversation[]): MisuseMetrics[] {
    if (!conversations || conversations.length === 0) {
      return [];
    }
    // Group conversations by user
    const userConversations = this.groupConversationsByUser(conversations);
    const userMetrics: MisuseMetrics[] = [];

    // Analyze each user's conversations
    for (const [userId, userData] of Object.entries(userConversations)) {
      const metrics = this.analyzeUserConversations(userId, userData);
      userMetrics.push(metrics);
    }

    this.metricsSubject.next(userMetrics);
    return userMetrics;
  }

  /**
   * Group conversations by user, filtering out users without both givenName and familyName
   */
  private groupConversationsByUser(conversations: Conversation[]): Record<string, {
    conversations: Conversation[];
    userName: string;
    email?: string;
  }> {
  return conversations.reduce((acc, conv) => {
    const givenName = conv.userData?.givenName?.trim() || '';
    const familyName = conv.userData?.familyName?.trim() || '';
    
    // Skip if user has no name
    if (!givenName || !familyName) {
      return acc;
    }
    
    const userName = [givenName, familyName].filter(Boolean).join(' ');
    
    if (!acc[conv.userId]) {
      acc[conv.userId] = {
        conversations: [],
        userName: userName,
        email: conv.userData?.email
      };
    }
    acc[conv.userId].conversations.push(conv);
    return acc;
  }, {} as Record<string, {
    conversations: Conversation[];
    userName: string;
    email?: string;
  }>);
}

  /**
   * Analyze a single user's conversations
   */
  private analyzeUserConversations(
    userId: string,
    userData: { conversations: Conversation[]; userName: string; email?: string }
  ): MisuseMetrics {
    const allMessages = this.getAllUserMessages(userData.conversations);
    const metrics: Omit<MisuseMetrics['metrics'], 'riskScore' | 'riskLevel'> = {
      // Message Frequency
      messagesPerMinute: this.calculateMessagesPerMinute(allMessages),
      messagesPerHour: this.calculateMessagesPerHour(allMessages),
      isHighFrequency: false,
      highFrequencyThreshold: this.defaultThresholds.highFrequency,
      highFrequencyWindow: this.defaultThresholds.highFrequencyWindow,
      
      // Duplicate Messages
      ...this.calculateDuplicateMetrics(allMessages),
      isHighDuplicate: false,
      duplicateThreshold: this.defaultThresholds.duplicateRatio,
      
      // Session Analysis
      ...this.calculateSessionMetrics(userData.conversations),
      isSuspiciousSession: false,
      minSessionLength: this.defaultThresholds.minSessionLength,
      
      // Intent Analysis
      ...this.calculateIntentMetrics(allMessages),
      isHighFallback: false,
      fallbackThreshold: this.defaultThresholds.fallbackRate,
      
      // Engagement
      ...this.calculateEngagementMetrics(allMessages)
    };

    // Calculate derived metrics
    const riskScore = this.calculateRiskScore(metrics);
    const riskLevel = this.determineRiskLevel(riskScore);
    const flaggedIssues = this.identifyIssues(metrics);

    return {
      userId,
      userName: userData.userName,
      email: userData.email,
      metrics: {
        ...metrics,
        riskScore,
        riskLevel,
      },
      lastUpdated: new Date(),
      flaggedIssues
    };
  }

  /**
   * Get all messages from conversations
   */
  private getAllUserMessages(conversations: Conversation[]): ChatMessage[] {
    return conversations.flatMap(conv => 
      conv.messages
        .filter(msg => msg.role === 'user')
        .map(msg => ({
          ...msg,
          conversationId: conv.id,
          timestamp: msg.timestamp || msg.createdAt
        }))
    ).sort((a, b) => 
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );
  }

  /**
   * Calculate messages per minute
   */
  private calculateMessagesPerMinute(messages: ChatMessage[]): number {
    if (messages.length < 2) return 0;
    
    const firstMessageTime = new Date(messages[0].timestamp).getTime();
    const lastMessageTime = new Date(messages[messages.length - 1].timestamp).getTime();
    const timeWindowMinutes = (lastMessageTime - firstMessageTime) / (1000 * 60);
    
    // Avoid division by zero and handle cases where all messages have the same timestamp
    return timeWindowMinutes > 0 ? messages.length / timeWindowMinutes : messages.length;
  }

  /**
   * Calculate messages per hour
   */
  private calculateMessagesPerHour(messages: ChatMessage[]): number {
    return this.calculateMessagesPerMinute(messages) * 60;
  }

  /**
   * Calculate duplicate message metrics
   */
  private calculateDuplicateMetrics(messages: ChatMessage[]): 
    Pick<MisuseMetrics['metrics'], 'duplicateMessageRatio' | 'totalMessages' | 'duplicateMessages'> {
    if (messages.length === 0) {
      return { duplicateMessageRatio: 0, totalMessages: 0, duplicateMessages: 0 };
    }

    const messageCounts = new Map<string, number>();
    let duplicateCount = 0;

    // Count occurrences of each message
    for (const msg of messages) {
      const content = (msg.message || msg.content || '').trim().toLowerCase();
      if (!content) continue;
      
      const count = (messageCounts.get(content) || 0) + 1;
      messageCounts.set(content, count);
      
      // Count as duplicate if seen before
      if (count > 1) {
        duplicateCount++;
      }
    }

    const totalMessages = messages.length;
    const duplicateMessageRatio = totalMessages > 0 ? duplicateCount / totalMessages : 0;

    return {
      duplicateMessageRatio,
      totalMessages,
      duplicateMessages: duplicateCount
    };
  }

  /**
   * Calculate session metrics
   */
  private calculateSessionMetrics(conversations: Conversation[]): 
    Pick<MisuseMetrics['metrics'], 'sessionCount' | 'avgSessionLength'> {
    if (conversations.length === 0) {
      return { sessionCount: 0, avgSessionLength: 0 };
    }

    const sessionCount = conversations.length;
    const totalMessages = conversations.reduce(
      (sum, conv) => sum + (conv.messages?.length || 0),
      0
    );
    const avgSessionLength = sessionCount > 0 ? totalMessages / sessionCount : 0;

    return {
      sessionCount,
      avgSessionLength
    };
  }

  /**
   * Calculate intent metrics
   */
  private calculateIntentMetrics(messages: ChatMessage[]): 
    Pick<MisuseMetrics['metrics'], 'fallbackRate' | 'avgConfidence'> {
    if (messages.length === 0) {
      return { fallbackRate: 0, avgConfidence: 0 };
    }

    let fallbackCount = 0;
    let totalConfidence = 0;
    let validConfidenceCount = 0;

    for (const msg of messages) {
      // Check for fallback (low confidence or no intent)
      if (msg.intent === 'FallbackIntent' || (msg.confidence !== undefined && msg.confidence < 0.5)) {
        fallbackCount++;
      }
      
      // Track confidence if available
      if (msg.confidence !== undefined) {
        totalConfidence += msg.confidence;
        validConfidenceCount++;
      }
    }

    const fallbackRate = messages.length > 0 ? fallbackCount / messages.length : 0;
    const avgConfidence = validConfidenceCount > 0 ? totalConfidence / validConfidenceCount : 0;

    return {
      fallbackRate,
      avgConfidence
    };
  }

  /**
   * Calculate engagement metrics
   */
  private calculateEngagementMetrics(messages: ChatMessage[]): 
    Pick<MisuseMetrics['metrics'], 'avgInputLength' | 'avgResponseLength' | 'engagementScore'> {
    if (messages.length === 0) {
      return { avgInputLength: 0, avgResponseLength: 0, engagementScore: 0 };
    }

    let totalInputLength = 0;
    let totalResponseLength = 0;
    let inputCount = 0;
    let responseCount = 0;

    for (const msg of messages) {
      const content = (msg.message || msg.content || '').trim();
      const length = content.length;
      
      if (msg.role === 'user') {
        totalInputLength += length;
        inputCount++;
      } else {
        totalResponseLength += length;
        responseCount++;
      }
    }

    const avgInputLength = inputCount > 0 ? totalInputLength / inputCount : 0;
    const avgResponseLength = responseCount > 0 ? totalResponseLength / responseCount : 0;
    
    // Engagement score is the ratio of response length to input length
    // Higher score means more detailed responses relative to inputs
    const engagementScore = avgInputLength > 0 ? avgResponseLength / avgInputLength : 0;

    return {
      avgInputLength,
      avgResponseLength,
      engagementScore
    };
  }

  /**
   * Calculate overall risk score (0-100)
   */
  private calculateRiskScore(metrics: Omit<MisuseMetrics['metrics'], 'riskScore' | 'riskLevel'>): number {
    let riskScore = 0;
    
    // Message frequency (up to 30 points)
    const frequencyFactor = Math.min(
      (metrics.messagesPerMinute / metrics.highFrequencyThreshold) * 30,
      30
    );
    riskScore += frequencyFactor;
    
    // Duplicate messages (up to 25 points)
    const duplicateFactor = (metrics.duplicateMessageRatio / metrics.duplicateThreshold) * 25;
    riskScore += Math.min(duplicateFactor, 25);
    
    // Session quality (up to 20 points)
    const sessionFactor = metrics.avgSessionLength < metrics.minSessionLength ? 
      (1 - (metrics.avgSessionLength / metrics.minSessionLength)) * 20 : 0;
    riskScore += sessionFactor;
    
    // Fallback rate (up to 15 points)
    const fallbackFactor = (metrics.fallbackRate / metrics.fallbackThreshold) * 15;
    riskScore += Math.min(fallbackFactor, 15);
    
    // Engagement (up to 10 points)
    // Lower engagement = higher risk
    const engagementFactor = (1 - Math.min(metrics.engagementScore, 1)) * 10;
    riskScore += engagementFactor;
    
    return Math.min(Math.round(riskScore * 10) / 10, 100); // Round to 1 decimal place, max 100
  }

  /**
   * Determine risk level based on score
   */
  private determineRiskLevel(score: number): 'low' | 'medium' | 'high' {
    if (score >= 70) return 'high';
    if (score >= 30) return 'medium';
    return 'low';
  }

  /**
   * Identify specific issues based on metrics
   */
  private identifyIssues(metrics: Omit<MisuseMetrics['metrics'], 'riskScore' | 'riskLevel'>): string[] {
    const issues: string[] = [];

    if (metrics.messagesPerMinute >= metrics.highFrequencyThreshold) {
      issues.push(`High message frequency (${metrics.messagesPerMinute.toFixed(1)}/min)`);
    }

    if (metrics.duplicateMessageRatio >= metrics.duplicateThreshold) {
      issues.push(`High duplicate message ratio (${(metrics.duplicateMessageRatio * 100).toFixed(0)}%)`);
    }

    if (metrics.avgSessionLength < metrics.minSessionLength) {
      issues.push(`Short session length (${metrics.avgSessionLength.toFixed(1)} messages/session)`);
    }

    if (metrics.fallbackRate >= metrics.fallbackThreshold) {
      issues.push(`High fallback rate (${(metrics.fallbackRate * 100).toFixed(0)}%)`);
    }

    if (metrics.engagementScore < 0.5) {
      issues.push('Low engagement (short responses relative to inputs)');
    }

    return issues;
  }
}
