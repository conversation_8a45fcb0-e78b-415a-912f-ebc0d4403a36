import { Injectable } from '@angular/core';
import { API, graphqlOperation, Storage } from 'aws-amplify';
import { Apollo, } from 'apollo-angular';
import { LIST_STAKEHOLDER_KNOWLEDGE } from 'src/app/shared/graphql/shared-graphql.queries';
import { TranscribeService } from 'src/app/pages/posts/services/transcribe.service';

@Injectable({
  providedIn: 'root'
})
export class StakeholderKnowledgeService {
  constructor(public readonly apollo: Apollo, private readonly transcribeService: TranscribeService,) { }

  async uploadStakeholderKnowledge(
    params: {
      userId: string;
      categoryId: string;
      subCategoryIds: string[];
      file: File;
      fileType: 'AUDIO' | 'VIDEO';
      knowledgeEntityType: 'STAKEHOLDER' | 'ORGANIZATION' | 'STUDENT';
      name: string;
      description: string;
      durationInMinutes: number;
      submittedBy: string;
      isPublic: boolean;
    },
    onProgress?: (progress: { loaded: number; total: number }) => void
  ) {
    try {
      // Upload file to S3
      const fileTypePrefix = params.fileType === 'AUDIO' ? 'audios' : 'videos';
      const fileKey = `stakeholder-knowledge/${params.userId}/${fileTypePrefix}/${Date.now()}-${params.file.name}`;
      
      await Storage.put(fileKey, params.file, {
        contentType: params.file.type,
        progressCallback: (progress: { loaded: number; total: number }) => {
          if (onProgress) {
            onProgress({
              loaded: progress.loaded,
              total: progress.total
            });
          }
        },
      });

      // Create the knowledge repository entry
      const createKnowledgeResult = await this.createKnowledgeRepositoryStore({
        ...params,
        fileUrl: fileKey
      });

      // Update or create StakeholderKnowledgeSummary
      await this.updateKnowledgeSummary(params.userId, params.categoryId, params.durationInMinutes);
      
      // Wait for transcription to complete
      try {
        await this.transcribeService.fetchVideoOrAudioTranscript(
          fileKey, 
          createKnowledgeResult?.data?.createKnowledgeRepositoryStore?.id, 
          "knowledge"
        );
      } catch (transcriptionError) {
        console.error('Error during transcription:', transcriptionError);
        // Don't fail the whole upload if transcription fails
      }

      // Refresh the knowledge item to get the latest data including transcription
      const updatedItem = await this.getKnowledgeRepositoryItem(
        createKnowledgeResult?.data?.createKnowledgeRepositoryStore?.id
      );

      return updatedItem ?? createKnowledgeResult;
    } catch (error) {
      console.error('Error uploading stakeholder knowledge:', error);
      throw error;
    }
  }

  // Make this method public for use in components
  public async getKnowledgeRepositoryItem(id: string): Promise<any> {
    const query = /* GraphQL */ `
      query GetKnowledgeRepositoryStore($id: ID!) {
        getKnowledgeRepositoryStore(id: $id) {
          id
          userId
          categoryId
          subCategoryIds
          fileType
          name
          description
          durationInMinutes
          fileUrl
          transcriptDetail {
            transcription
            summary
            metadata
          }
          status
          createdAt
          updatedAt
        }
      }
    `;

    try {
      const response = await API.graphql(graphqlOperation(query, { id }));
      return (response as any)?.data?.getKnowledgeRepositoryStore;
    } catch (error) {
      console.error('Error fetching knowledge repository item:', error);
      return null;
    }
  }

  // Expose a public method to trigger transcription and return the result
  public async transcribeVideoOrAudio(fileKey: string, knowledgeId: string, type: string = "knowledge"): Promise<any> {
    try {
      return await this.transcribeService.fetchVideoOrAudioTranscript(fileKey, knowledgeId, type);
    } catch (error) {
      console.error('Error during transcription:', error);
      throw error;
    }
  }

  async createKnowledgeRepositoryStore(params: {
    userId: string;
    categoryId: string;
    subCategoryIds: string[];
    fileUrl: string;
    fileType: 'AUDIO' | 'VIDEO';
    knowledgeEntityType: 'STAKEHOLDER' | 'ORGANIZATION' | 'STUDENT';
    name: string;
    description: string;
    durationInMinutes: number;
    submittedBy: string;
    isPublic: boolean;
  }) {
    const mutation = /* GraphQL */ `
      mutation CreateKnowledgeRepositoryStore($input: CreateKnowledgeRepositoryStoreInput!) {
        createKnowledgeRepositoryStore(input: $input) {
          id
          userId
          categoryId
          subCategoryIds
          fileType
          name
          description
          durationInMinutes
          fileUrl
          status
          createdAt
          updatedAt
        }
      }
    `;

    const createInput = {
      input: {
        name: params.name,
        fileUrl: params.fileUrl,
        fileType: params.fileType,
        entityType: params.knowledgeEntityType,
        durationInMinutes: params.durationInMinutes,
        userId: params.userId,
        categoryId: params.categoryId,
        subCategoryIds: params.subCategoryIds,
        submittedBy: params.submittedBy,
        description: params.description,
        status: 'PENDING',
        isPublic: params.isPublic,
      }
    };

    return await API.graphql(graphqlOperation(mutation, createInput));
  }

  async updateKnowledgeSummary(userId: string, categoryId: string, newDurationMinutes: number): Promise<void> {
    try {
      // First, try to get existing summary
      const getSummaryQuery = /* GraphQL */ `
        query ListKnowledgeRepositoryStoreSummaries($filter: ModelKnowledgeRepositoryStoreSummaryFilterInput) {
          listKnowledgeRepositoryStoreSummaries(filter: $filter) {
            items {
              id
              userId
              categoryId
              totalMinutes
              lastUploadDate
              isDeleted
              createdAt
              updatedAt
              _version
              _lastChangedAt
            }
          }
        }
      `;

      const summaryResult = await API.graphql(
        graphqlOperation(getSummaryQuery, {
          filter: {
            userId: { eq: userId },
            categoryId: { eq: categoryId },
            isDeleted: { eq: "false" }
          }
        })
      ) as any;

      const existingSummary = summaryResult.data?.listKnowledgeRepositoryStoreSummaries?.items?.[0];

      if (existingSummary) {
        // Update existing summary
        const updateMutation = /* GraphQL */ `
          mutation UpdateKnowledgeRepositoryStoreSummary($input: UpdateKnowledgeRepositoryStoreSummaryInput!) {
            updateKnowledgeRepositoryStoreSummary(input: $input) {
              id
              totalMinutes
              lastUploadDate
              _version
              _lastChangedAt
            }
          }
        `;

        const newTotalMinutes = parseFloat((existingSummary.totalMinutes + newDurationMinutes).toFixed(2));

        await API.graphql(
          graphqlOperation(updateMutation, {
            input: {
              id: existingSummary.id,
              userId: userId,
              categoryId: categoryId,
              totalMinutes: newTotalMinutes,
              lastUploadDate: new Date().toISOString(),
              _version: existingSummary._version
            }
          })
        );
      } else {
        // Create new summary
        const createMutation = /* GraphQL */ `
          mutation CreateKnowledgeRepositoryStoreSummary($input: CreateKnowledgeRepositoryStoreSummaryInput!) {
            createKnowledgeRepositoryStoreSummary(input: $input) {
              id
              totalMinutes
              lastUploadDate
              _version
              _lastChangedAt
            }
          }
        `;

        await API.graphql(
          graphqlOperation(createMutation, {
            input: {
              userId: userId,
              categoryId: categoryId,
              totalMinutes: parseFloat(newDurationMinutes.toFixed(2)),
              lastUploadDate: new Date().toISOString(),
              isDeleted: "false"
            }
          })
        );
      }
    } catch (error) {
      console.error('Error updating knowledge summary:', error);
      throw error;
    }
  }

  async updateKnowledgeSummaryForOrganization(organizationId: string, categoryId: string, newDurationMinutes: number): Promise<void> {
    try {
      // First, try to get existing summary
      const getSummaryQuery = /* GraphQL */ `
        query ListKnowledgeRepositoryStoreSummaries($filter: ModelKnowledgeRepositoryStoreSummaryFilterInput) {
          listKnowledgeRepositoryStoreSummaries(filter: $filter) {
            items {
              id
              organizationId
              categoryId
              totalMinutes
              lastUploadDate
              isDeleted
              createdAt
              updatedAt
              _version
              _lastChangedAt
            }
          }
        }
      `;

      const summaryResult = await API.graphql(
        graphqlOperation(getSummaryQuery, {
          filter: {
            organizationId: { eq: organizationId },
            categoryId: { eq: categoryId },
            isDeleted: { eq: "false" }
          }
        })
      ) as any;

      const existingSummary = summaryResult.data?.listKnowledgeRepositoryStoreSummaries?.items?.[0];

      if (existingSummary) {
        // Update existing summary
        const updateMutation = /* GraphQL */ `
          mutation UpdateKnowledgeRepositoryStoreSummary($input: UpdateKnowledgeRepositoryStoreSummaryInput!) {
            updateKnowledgeRepositoryStoreSummary(input: $input) {
              id
              totalMinutes
              lastUploadDate
              _version
              _lastChangedAt
            }
          }
        `;

        const newTotalMinutes = parseFloat((existingSummary.totalMinutes + newDurationMinutes).toFixed(2));

        await API.graphql(
          graphqlOperation(updateMutation, {
            input: {
              id: existingSummary.id,
              organizationId: organizationId,
              categoryId: categoryId,
              totalMinutes: newTotalMinutes,
              lastUploadDate: new Date().toISOString(),
              _version: existingSummary._version
            }
          })
        );
      } else {
        // Create new summary
        const createMutation = /* GraphQL */ `
          mutation CreateKnowledgeRepositoryStoreSummary($input: CreateKnowledgeRepositoryStoreSummaryInput!) {
            createKnowledgeRepositoryStoreSummary(input: $input) {
              id
              totalMinutes
              lastUploadDate
              _version
              _lastChangedAt
            }
          }
        `;

        await API.graphql(
          graphqlOperation(createMutation, {
            input: {
              organizationId: organizationId,
              categoryId: categoryId,
              totalMinutes: parseFloat(newDurationMinutes.toFixed(2)),
              lastUploadDate: new Date().toISOString(),
              isDeleted: "false"
            }
          })
        );
      }
    } catch (error) {
      console.error('Error updating knowledge summary:', error);
      throw error;
    }
  }

  async deleteStakeholderKnowledge(id: string): Promise<void> {
    try {
      // First get the knowledge item with all necessary fields
      const getQuery = /* GraphQL */ `
        query GetKnowledgeRepositoryStore($id: ID!) {
          getKnowledgeRepositoryStore(id: $id) {
            id
            userId
            categoryId
            durationInMinutes
            fileUrl
            _version
            _deleted
            _lastChangedAt
            isDeleted
          }
        }
      `;

      const knowledgeResult = await API.graphql(
        graphqlOperation(getQuery, { id })
      ) as any;

      const knowledge = knowledgeResult.data?.getKnowledgeRepositoryStore;

      if (!knowledge) {
        throw new Error('Knowledge item not found');
      }

      if (knowledge._deleted || knowledge.isDeleted === "true") {
        throw new Error('Knowledge item already deleted');
      }

      // First try to delete the file from S3
      try {
        await Storage.remove(knowledge.fileUrl);
      } catch (s3Error) {
        console.error('Error deleting file from S3:', s3Error);
        // Continue with record deletion even if S3 deletion fails
      }

      // Update the summary by subtracting the duration
      await this.updateKnowledgeSummary(
        knowledge.userId,
        knowledge.categoryId,
        -knowledge.durationInMinutes // Subtract the duration
      );

      // Update the knowledge item to mark as deleted
      const updateMutation = /* GraphQL */ `
        mutation UpdateKnowledgeRepositoryStore($input: UpdateKnowledgeRepositoryStoreInput!) {
          updateKnowledgeRepositoryStore(input: $input) {
            id
            _version
            _deleted
            _lastChangedAt
            isDeleted
          }
        }
      `;

      const updateResult = await API.graphql(
        graphqlOperation(updateMutation, {
          input: {
            id: knowledge.id,
            _version: knowledge._version,
            isDeleted: "true"
          }
        })
      ) as any;

      if (!updateResult.data?.updateKnowledgeRepositoryStore?.id) {
        throw new Error('Failed to update knowledge item');
      }

      // Now perform the actual delete
      const deleteMutation = /* GraphQL */ `
        mutation DeleteKnowledgeRepositoryStore($input: DeleteKnowledgeRepositoryStoreInput!) {
          deleteKnowledgeRepositoryStore(input: $input) {
            id
            _version
            _deleted
            _lastChangedAt
          }
        }
      `;

      const deleteResult = await API.graphql(
        graphqlOperation(deleteMutation, {
          input: {
            id: knowledge.id,
            _version: updateResult.data.updateKnowledgeRepositoryStore._version
          }
        })
      ) as any;

      if (!deleteResult.data?.deleteKnowledgeRepositoryStore?.id) {
        throw new Error('Failed to delete knowledge item');
      }

    } catch (error: any) {
      console.error('Error deleting stakeholder knowledge:', error);
      if (error?.errors?.[0]?.message) {
        throw new Error(error.errors[0].message);
      } else if (error?.message) {
        throw new Error(error.message);
      } else {
        throw new Error('Error deleting file');
      }
    }
  }

  async uploadFile(file: File, knowledgeId: string, entityId: string, type: 'stakeholder-knowledge' | 'organization-knowledge'): Promise<{ fileUrl: string }> {
    try {
      const fileTypePrefix = file.type.startsWith('audio/') ? 'audios' : 'videos';
      const fileKey = `${type}/${entityId}/${fileTypePrefix}/${Date.now()}-${file.name}`;

      await Storage.put(fileKey, file, {
        contentType: file.type,
        progressCallback: (progress) => {
          console.log(`Uploaded: ${progress.loaded}/${progress.total}`);
        },
      });

      return { fileUrl: fileKey };
    } catch (error) {
      console.error('Error uploading file:', error);
      throw error;
    }
  }

  async getFileUrl(fileKey: string): Promise<string> {
    try {
      return await Storage.get(fileKey);
    } catch (error) {
      console.error('Error getting file URL:', error);
      throw error;
    }
  }

  async listStakeholderKnowledge2(userId: string): Promise<any> {
    return this.apollo.query({
      query: LIST_STAKEHOLDER_KNOWLEDGE,
      variables: {
        filter: {
          userId: { eq: userId },
        }
      }
    })
  }

  // Generic method to list knowledge repository stores by any filter
  async listKnowledgeRepositoryStores(filter: any) {
    try {
      const query = /* GraphQL */ `
        query ListKnowledgeRepositoryStores($filter: ModelKnowledgeRepositoryStoreFilterInput) {
          listKnowledgeRepositoryStores(filter: $filter) {
            items {
              id
              userId
              organizationId
              categoryId
              subCategoryIds
              fileType
              name
              description
              durationInMinutes
              fileUrl
              transcriptDetail {
                transcription
                summary
                metadata
              }
              status
              createdAt
              updatedAt
              _deleted
              _version
              _lastChangedAt
            }
            nextToken
            startedAt
          }
        }
      `;
      const variables = { filter };

      const result = await API.graphql(graphqlOperation(query, variables));
      // Filter out deleted items on the client side
      const filteredResult = {
        ...result,
        data: {
          ...result.data,
          listKnowledgeRepositoryStores: {
            ...result.data.listKnowledgeRepositoryStores,
            items: result.data.listKnowledgeRepositoryStores.items.filter((item: any) => !item._deleted)
          }
        }
      };
      return filteredResult;
    } catch (error) {
      console.error('Error listing knowledge repository stores:', error);
      throw error;
    }
  }
}