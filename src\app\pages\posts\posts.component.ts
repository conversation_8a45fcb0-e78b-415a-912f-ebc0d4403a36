import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { Storage } from 'aws-amplify';

import { SharedService } from '../../shared/services/shared.service';
import { PostsService } from './services/posts.service';
import { FilterPipe } from './pipes/filter-pipe';
import { OrganizationsService } from '../organizations/services/organizations.service';

@Component({
  selector: 'app-posts',
  templateUrl: './posts.component.html',
  styleUrls: ['./posts.component.scss'],
})
export class PostsComponent implements OnInit {
  postsCount: number = 0;

  exportType: string = '';
  showOptions: string = '';
  currentTab: string = 'Card Panel';
  sortBy: string = 'Recently Updated';
  postId: string = '';
  filters: any;
  postDetails: any;
  postsList: any[] = [];
  postImages: string[] = [];
  cognitoUsersList: any[] = [];

  constructor(
    private readonly router: Router,
    private readonly toastr: ToastrService,
    private readonly filterPipe: FilterPipe,
    private readonly modalService: NgbModal,
    public postsService: PostsService,
    private readonly spinner: NgxSpinnerService,
    public readonly sharedService: SharedService,
    private readonly organizationsService: OrganizationsService,
  ) { }

  ngOnInit(): void {
    this.getPostsList();
    if (this.postsService.filters) {
      this.filters = this.postsService.filters;
    }
  }

  async getImagePreviewsForPost(postObj: any): Promise<void> {
    postObj.imagePreview = [];

    if (postObj?.images) {
      for (const imageKeyRaw of postObj.images) {
        const imageKey = `post/${postObj.id}/images/${imageKeyRaw.trim()}`;
        try {
          const result = await Storage.get(imageKey, { level: 'public' });
          postObj.imagePreview.push(result);
        } catch {
          // Handle error or ignore
        }
      }
    }

    if (postObj?.videosThumbnail?.length && postObj.videosThumbnail[0] !== '') {
      for (const imageKeyRaw of postObj.videosThumbnail) {
        const imageKey = `post/${postObj.id}/videos-thumbnail/${imageKeyRaw.trim()}`;
        try {
          const result = await Storage.get(imageKey, { level: 'public' });
          postObj.imagePreview.push(result);
        } catch {
          // Handle error or ignore
        }
      }
    }
  }

  getPostsList() {
    this.sharedService.isLoading.next(true);

    this.postsService.getPostsList().subscribe({
      next: (response: any) => {
        this.handlePostsResponse(response).catch(error => {
          // Handle async errors here if needed
          console.error('Error processing posts:', error);
          this.sharedService.isLoading.next(false);
          this.spinner.hide();
        });
      },
      error: (error: any) => {
        this.router.navigate(['/events']);
        this.sharedService.isLoading.next(false);
        this.spinner.hide();
      }
    });
  }

  // Separate async method to handle the response processing
  private async handlePostsResponse(response: any) {
    this.postsList = response?.data?.SubmissionByDate?.items.filter((el: any) => !el?._deleted);

    if (this.postsList?.length) {
      for (const postObj of this.postsList) {
        await this.getImagePreviewsForPost(postObj);
      }
    }

    this.postsCount = this.postsList.length;
    this.applyFilter(this.filters, this.postsService.currentPage || 1);

    this.sharedService.isLoading.next(false);
    this.spinner.hide();
  }



  applyFilter(value: any, currentPage?: number) {
    this.filters = value;

    this.postsCount = this.filterPipe.transform(
      this.postsList,
      this.filters,
      this.sortBy
    )?.length;

    this.sharedService.currentPage.next(currentPage || 1);
  }

  getRandomColor(index: number) {
    return this.sharedService.randomColor(index);
  }

  setFilters(id: string) {
    this.postsService.filters = this.filters;
    this.postsService.currentPage = this.currentPage;

    this.router.navigate(['/activity-submission/view-submission', id]);
  }

  openImageCarouselModal(content: any, images: any) {
    this.postImages = images;

    this.modalService.open(content, {
      size: 'md',
    });
  }

  openDeleteConfirmationModal(content: any, postDetails: any) {
    this.postDetails = postDetails;
    this.modalService.open(content, {
      size: 'md',
    });
  }

  closeModal() {
    this.modalService.dismissAll();
  }

  setCurrentTab(tab: string) {
    this.currentTab = tab;
  }

  get currentPage() {
    return this.sharedService.currentPage.value;
  }

  // Media type badge helper methods
  hasVideo(post: any): boolean {
    return post?.videos && post.videos.length > 0;
  }

  hasAudio(post: any): boolean {
    return post?.audios && post.audios.length > 0;
  }

  hasImage(post: any): boolean {
    return post?.images && post.images.length > 0;
  }

  getMediaTypeBadges(post: any): string[] {
    const badges: string[] = [];
    if (this.hasVideo(post)) badges.push('video');
    if (this.hasAudio(post)) badges.push('audio');
    if (this.hasImage(post)) badges.push('image');
    return badges;
  }

  get getCountStartValue() {
    return this.sharedService.getCountStartValue(this.postsCount);
  }

  get getCountEndValue() {
    return this.sharedService.getCountEndValue(this.postsCount);
  }

  goToDetailsPage(id: string) {
    this.router.navigate(['activity-submission/view-submission', id]);
  }

  updatePost(content: any, postDetails: any) {
    if (!postDetails?.categoryType) {
      this.postId = postDetails?.id
      this.modalService.open(content, {
        size: 'md',
      });
      return;
    }
    const data = {
      id: postDetails.id,
      text: postDetails.text,
      location: postDetails.location,
      _version: postDetails._version,
      submissionStatus: postDetails.submissionStatus,
    };

    data.submissionStatus = postDetails.submissionStatus;

    this.postsService.updatePost(data).subscribe({
      next: () => {
        this.sharedService
          .generateLog({
            type: data.submissionStatus === 'false' ? 'DEACTIVATED' : 'ACTIVATED',
            moduleId: postDetails.id,
            moduleName: postDetails.text ?? 'Homework Submission',
            moduleType: 'homework-submission',
            requestStatus: 'SYSTEM_APPROVED',
            activityType: "STORYTELLING",
            cityId: this.sharedService.defaultCityId.value
          })
          .subscribe();

        this.toastr.success(
          data.submissionStatus === 'true'
            ? 'Successfully activated the story!'
            : 'Successfully deactivated the story!'
        );

        this.postsList.forEach(
          (element) =>
          (element.submissionStatus =
            element.id === data.id ? data.submissionStatus : element.submissionStatus)
        );

        // Trigger transcription if submission was approved
        if (data.submissionStatus === 'APPROVED') {
          this.triggerTranscriptionForApproval(postDetails);
        }
      },
    });
  }

  deletePost() {
    const data = {
      id: this.postDetails.id,
      text: this.postDetails.text,
      isDeleted: "true",
      location: this.postDetails.location,
      _version: this.postDetails._version,
    };
    this.sharedService.isLoading.next(true);

    this.postsService.updatePost(data).subscribe({
      next: ({ data }: any) => {
        this.postsService
          .deletePost(this.postDetails.id, data.updateSubmission._version)
          .subscribe({
            next: ({ data }) => {
              if (this.postDetails.memberId && this.postDetails.memberId.length > 0) {
                this.organizationsService
                  .getOrganizationById(this.postDetails?.memberId[0])
                  .subscribe((orgData: any) => {
                    this.sharedService
                      .generateLog({
                        type: 'DELETED',
                        moduleId: this.postDetails.id,
                        moduleName: data.DeleteSubmission.text ?? 'Homework Submission',
                        moduleType: 'homework-submission',
                        requestStatus: 'SYSTEM_APPROVED',
                        relatedTo: 'member',
                        relatedId: this.postDetails?.memberId[0],
                        relatedName: orgData?.data?.getOrganizations?.name,
                        activityType: "STORYTELLING",
                        cityId: this.sharedService.defaultCityId.value
                      }).subscribe()
                  })
              }
              else {
                this.sharedService
                  .generateLog({
                    type: 'DELETED',
                    moduleId: this.postDetails.id,
                    moduleName: data.DeleteSubmission.text ?? 'Homework Submission',
                    moduleType: 'homework-submission',
                    requestStatus: 'SYSTEM_APPROVED',
                    activityType: "STORYTELLING",
                    cityId: this.sharedService.defaultCityId.value
                  }).subscribe()
              }

              this.sharedService.isLoading.next(false);
              this.toastr.success('Successfully deleted submission.');
              this.postsList = this.postsList.filter(
                (element) => element.id !== this.postDetails.id
              );
              if (this.postDetails.images && this.postDetails.images?.length > 0) {
                this.postDetails.images.forEach((element: string) =>
                  Storage.remove(element, {
                    level: 'public',
                  })
                );
              }
              if (this.postDetails.videos && this.postDetails.videos.length > 0) {
                this.postDetails.videos.forEach((element: string) =>
                  Storage.remove(element, {
                    level: 'public',
                  })
                );
              }
              this.filterPipe.transform(
                this.postsList,
                this.filters,
                this.sortBy
              );
              this.postsCount = this.filterPipe.transform(
                this.postsList,
                this.filters,
                this.sortBy
              ).length;
              this.sharedService.currentPage.next(1);
              this.closeModal();
            },
            error: (error: any) => {
            },
          });
      },
    });
  }

  private async triggerTranscriptionForApproval(postDetails: any): Promise<void> {
    try {
      const hasMedia = (postDetails.videos && postDetails.videos.length > 0) ||
                      (postDetails.audios && postDetails.audios.length > 0);

      if (!hasMedia) {
        return;
      }

      if (postDetails.transcriptDetail) {
        return;
      }

      await this.postsService.triggerTranscriptionForApprovedSubmission(
        postDetails.id,
        postDetails.videos ?? [],
        postDetails.audios ?? []
      );

    } catch (error) {
      console.error('Error triggering transcription for approved submission:', error);
      this.toastr.error('Failed to start transcription process');
    }
  }
}
