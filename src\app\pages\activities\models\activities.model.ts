export interface ModuleUser {
  id: string;
  name: string;
  email: string;
  // Add other properties from the API response as needed
  [key: string]: any;
}

export interface ActivityObject {
  id?: string;
  type:
  | 'CREATED'
  | 'UPDATED'
  | 'DELETED'
  | 'ACTIVATED'
  | 'DEACTIVATED'
  | 'ADDED'
  | 'TOKENS'
  | 'REMOVED';
  activityType?:
  | 'MEMBERSHIP'
  | 'PROJECT'
  | 'STORYTELLING'
  | 'FUNDING';
  requestStatus?:
  | 'SYSTEM_APPROVED'
  | 'ADMIN_APPROVED'
  | 'ADMIN_DENIED'
  | 'PENDING_APPROVAL';
  cityId?: string;
  activityTokens?: string;
  moduleId: string;
  moduleName: string;
  moduleImageUrl?: string;
  moduleType: string;
  moduleUser?: ModuleUser;
  isRelationship?: boolean;
  relatedTo?: string;
  relatedId?: string;
  relatedName?: string;
  createdUserId?: string;
  createdUserName?: string;
  updatedData?: Object;
  createdAt?: Date;
}

export interface ActivitiesList {
  activitiesByDate: { items: ActivityObject[] };
}
