/**
 * Apollo Client service for GraphQL operations
 * Provides reusable Apollo Client configuration and setup for Lambda functions
 */

const { createHttpLink } = require('apollo-link-http');
const { ApolloClient, InMemoryCache } = require('@apollo/client/core');
const fetch = require('node-fetch');
const { createLogger } = require('../utils/logger');

const logger = createLogger('apolloClientService');

/**
 * Create Apollo Client configuration options
 * @param {Object} options - Configuration options
 * @param {string} options.endpoint - GraphQL endpoint URL
 * @param {string} options.authToken - Authorization token
 * @param {string} options.apiKey - API key
 * @param {string} options.requestId - Request ID for tracing
 * @param {string} options.userId - User ID
 * @returns {Object} Apollo Client configuration
 */
const createApolloClientConfig = ({
  endpoint,
  authToken = '',
  apiKey = '',
  requestId = '',
  userId = 'anonymous'
}) => {
  if (!endpoint) {
    throw new Error('GraphQL endpoint is required');
  }

  // Create HTTP link with proper headers
  const link = createHttpLink({
    uri: endpoint,
    fetch: fetch,
    headers: {
      'Authorization': authToken,
      'Content-Type': 'application/json',
      'x-api-key': apiKey,
      'x-request-id': requestId,
      'x-user-id': userId
    }
  });

  // Configure Apollo client with error handling
  const config = {
    link,
    cache: new InMemoryCache(),
    defaultOptions: {
      watchQuery: {
        fetchPolicy: 'network-only',
        errorPolicy: 'all',
      },
      query: {
        fetchPolicy: 'network-only',
        errorPolicy: 'all',
      },
      mutate: {
        errorPolicy: 'all',
      },
    },
  };

  return config;
};

/**
 * Create a configured Apollo Client instance
 * @param {Object} options - Configuration options
 * @param {string} options.endpoint - GraphQL endpoint URL
 * @param {string} options.authToken - Authorization token
 * @param {string} options.apiKey - API key
 * @param {string} options.requestId - Request ID for tracing
 * @param {string} options.userId - User ID
 * @returns {ApolloClient} Configured Apollo Client instance
 */
const createApolloClient = (options) => {
  try {
    const config = createApolloClientConfig(options);
    const client = new ApolloClient(config);
    
    logger.debug('Apollo Client created successfully', {
      endpoint: options.endpoint,
      hasAuthToken: !!options.authToken,
      requestId: options.requestId,
      userId: options.userId
    });
    
    return client;
  } catch (error) {
    logger.error('Failed to create Apollo Client', {
      error: error.message,
      endpoint: options.endpoint,
      requestId: options.requestId
    });
    throw error;
  }
};

/**
 * Extract authentication token from Lambda event
 * @param {Object} event - Lambda event object
 * @returns {string} Authentication token or empty string
 */
const extractAuthToken = (event) => {
  return event?.request?.headers?.authorization || 
         event?.headers?.authorization ||
         event?.authorization?.token ||
         '';
};

/**
 * Create Apollo Client from Lambda event context
 * @param {Object} event - Lambda event object
 * @param {Object} context - Lambda context object
 * @param {string} endpoint - GraphQL endpoint URL
 * @returns {ApolloClient} Configured Apollo Client instance
 */
const createApolloClientFromEvent = (event, context, endpoint) => {
  const authToken = extractAuthToken(event);
  const requestId = context?.awsRequestId || '';
  const userId = event?.identity?.sub || 'anonymous';
  const apiKey = process.env.API_KEY || '';

  return createApolloClient({
    endpoint,
    authToken,
    apiKey,
    requestId,
    userId
  });
};

module.exports = {
  createApolloClient,
  createApolloClientConfig,
  createApolloClientFromEvent,
  extractAuthToken
};
