import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';

import { getCSSVariableValue } from '../../../../_metronic/kt/_utils';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { SharedService } from 'src/app/shared/services/shared.service';
import { ToastrService } from 'ngx-toastr';
import { OrganizationsService } from 'src/app/pages/organizations/services/organizations.service';


@Component({
  selector: 'app-activities-goal-widget',
  templateUrl: './activities-goal-widget.component.html',
  styleUrls: ['./activities-goal-widget.component.scss'],
})
export class ActivitiesGoalWidgetComponent implements OnInit, OnChanges {
  @Input() activitiesList: any;
  @Input() chartHeight: string;
  maxDate = new Date();
  @Input() currentSetting: any;
  @Input() chartColor: string = '';

  chartOptions: any = {};
  mostActiveActivity: any;
  totalPercentage: number = 0;
  ktMenuStaticUi: any = false;
  filterForm: FormGroup = new FormGroup({});
  membersSize: any;
  studentSize: any;
  stackSize: any;

  constructor(
    private readonly formBuilder: FormBuilder,
    private readonly router: Router,
    public sharedService: SharedService,
    private readonly toastr: ToastrService,
    private readonly organizatonService: OrganizationsService
  ) { }

  ngOnInit(): void {
    this.initializeForm();
    this.chartOptions = getChartOptions(this.chartHeight, this.chartColor, this.totalPercentage);
    this.getTotalMembersId();
    this.getTotalUserId()
  }
  ngOnChanges(change: SimpleChanges) {
    if (change.activitiesList || change.currentSetting) {
      this.filterForm.patchValue({
        activityStartDate: this.getDates(this.currentSetting.sortBy.toLowerCase())[0],
        activityEndDate: this.getDates(this.currentSetting.sortBy.toLowerCase())[1],
      });
      this.applyFilter();
    }
  }

  /**
   * * Initialize Form
   * ? This function is initializing the form used in the template of this component.
   */
  initializeForm() {
    this.filterForm = this.formBuilder.group({
      activityStartDate: '',
      activityEndDate: '',
    });
  }

  /**
   * * Calculate Percentage
   * ? This function is used to calculate the percentage value that is shown in the chart.
   * @returns This function returns the rounded percentage value.
   */
  calculatePercentage(): any {
    // Get dates from form with null checks
    const startDateValue = this.filterForm.get('activityStartDate')?.value;
    const endDateValue = this.filterForm.get('activityEndDate')?.value;

    // If either date is missing, return early
    if (!startDateValue || !endDateValue) {
      return 0;
    }

    const memberArray: any[] = [];
    const studentArray: any[] = [];
    let stakeholderArray: any[] = [];

    // Create date objects with validation
    const startDate = new Date(startDateValue);
    const endDate = new Date(endDateValue);
    
    // Validate dates
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return 0;
    }

    const endOfDay = new Date(endDate);
    endOfDay.setHours(23, 59, 59, 999);

    const filteredActivities = (this.activitiesList || []).filter((activity: any) => {
      if (!activity?.createdAt) return false;
      
      const createdAt = new Date(activity.createdAt);
      if (isNaN(createdAt.getTime())) return false;
      
      const createdAtTimestamp = createdAt.getTime();
      return (
        createdAtTimestamp >= startDate.getTime() &&
        createdAtTimestamp <= endOfDay.getTime()
      );
    });

    filteredActivities.forEach((activity: any) => {
      if (activity.moduleType !== 'school' && activity.moduleType !== 'business') {
        if ((activity?.moduleMember != null && (activity.moduleType === 'member' || activity.moduleType === 'association') && activity?.moduleMember?.id) || ((activity.moduleType !== 'member') && ['member', 'organization', 'organizations'].indexOf(activity.relatedTo?.toLowerCase()) !== -1)) {
          memberArray.push(activity);
        }
        if ((activity?.moduleUser != null && (activity.moduleType === 'student' || activity.moduleType === 'association') && !activity?.moduleUser?.isStakeholder && !activity?.moduleUser?._deleted) || (activity?.relatedUser != null && ['member', 'course', 'project', 'event'].indexOf(activity.moduleType.toLowerCase()) === -1 && ['person', 'student'].indexOf(activity.relatedTo?.toLowerCase()) !== -1 && !activity?.relatedUser?.isStakeholder && !activity?.relatedUser?._deleted)) {
          studentArray.push(activity);
        }
      }
    });

    stakeholderArray = filteredActivities.filter(
      (activity: any) =>
        (activity?.moduleUser != null && !!activity?.moduleUser?.isStakeholder && !activity?.moduleUser?._deleted) &&
        ((activity.moduleType === 'student' || activity.moduleType === 'association') || (activity?.relatedUser != null && ['member', 'course', 'project', 'event'].indexOf(activity.moduleType.toLowerCase()) === -1 && ['person', 'student'].indexOf(activity.relatedTo?.toLowerCase()) !== -1 && !!activity?.relatedUser?.isStakeholder && !activity?.relatedUser?._deleted))
    );

    const memberIdArray: any[] = [];
    memberArray.forEach(ex => {
      if (ex.relatedMember?.id && memberIdArray.indexOf(ex.relatedMember?.id) === -1) {
        memberIdArray.push(ex.relatedMember?.id);
      }
      if (ex.moduleMember?.id && memberIdArray.indexOf(ex.moduleMember?.id) === -1) {
        memberIdArray.push(ex.moduleMember?.id);
      }
    });

    const studentIdArray: any[] = [];
    studentArray.forEach(ex => {
      if (ex.moduleUser?.id && ex.moduleUser?.isStakeholder === false) {
        if (studentIdArray.indexOf(ex.moduleUser?.id) === -1) {
          studentIdArray.push(ex.moduleUser?.id);
        }
      }
      if (ex.relatedUser?.id && ex.relatedUser?.isStakeholder === false) {
        if (studentIdArray.indexOf(ex.relatedUser?.id) === -1) {
          studentIdArray.push(ex.relatedUser?.id);
        }
      }
    });

    const stackholderIdArray: any[] = [];
    stakeholderArray.forEach(ex => {
      if (ex.moduleUser?.id && ex.moduleUser?.isStakeholder === true) {
        if (stackholderIdArray.indexOf(ex.moduleUser?.id) === -1) {
          stackholderIdArray.push(ex.moduleUser?.id);
        }
      }
      if (ex.relatedUser?.id && ex.relatedUser?.isStakeholder === true) {
        if (stackholderIdArray.indexOf(ex.relatedUser?.id) === -1) {
          stackholderIdArray.push(ex.relatedUser?.id);
        }
      }
    });

    const totalMembersIds = memberIdArray.length;
    const totalStudentsIds = studentIdArray.length;
    const totalStakeholdersIds = stackholderIdArray.length;

    const memberPercentageById = (totalMembersIds / this.membersSize) * 100;
    const studentPercentageById = (totalStudentsIds / this.studentSize) * 100;
    const stakeholderPercentageById = (totalStakeholdersIds / this.stackSize) * 100;

    let overallPercentage: number;
    let filteredArray: any[];
    if (this.currentSetting.activityCategory === 'member') {
      overallPercentage = memberPercentageById;
      filteredArray = memberArray;
    } else if (this.currentSetting.activityCategory === 'stakeholder') {
      overallPercentage = stakeholderPercentageById;
      filteredArray = stakeholderArray;
    } else {
      overallPercentage = studentPercentageById;
      filteredArray = studentArray;
    }

    const roundedPercentage = Math.round(overallPercentage);
    this.mostActiveActivity = this.getMostActiveActivity(filteredArray);

    return roundedPercentage;
  }

  goToProfilePage(): void {
    const activity = this.mostActiveActivity;
    if (!activity) {
      this.toastr.warning('No entity found.');
      return;
    }

    const id = this.getActivityId(activity);
    const encryptedId = this.sharedService.getEncryptedId(id);

    if (!this.isProfileModule(activity.moduleType)) {
      this.navigateByRelatedTo(activity, id, encryptedId);
      return;
    }

    switch (activity.moduleType) {
      case 'member':
      case 'funding':
        this.router.navigate(['/members/view-member', encryptedId]);
        break;

      case 'story':
        this.router.navigate(['/activity-submission/view-submission/', id]);
        break;

      default:
        if (!activity.moduleUser?.isStakeholder && activity.moduleType !== 'funding') {
          this.router.navigate(['/students/view-student', id]);
        } else {
          this.router.navigate(['/stakeholders/view-stakeholder', id]);
        }
        break;
    }
  }

  private isProfileModule(moduleType: string): boolean {
    const profileModules = ["student", "association", "stakeholder", "member", "update-profile"];
    return profileModules.includes(moduleType);
  }

  private getActivityId(activity: any): string {
    return this.isProfileModule(activity.moduleType) ? activity.moduleId : activity.relatedId;
  }

  private navigateByRelatedTo(activity: any, id: string, encryptedId: string): void {
    const type = activity.relatedTo?.toLowerCase();

    switch (type) {
      case 'member':
      case 'organization':
      case 'organizations':
        this.router.navigate(['/members/view-member', encryptedId]);
        break;

      case 'student':
        this.router.navigate(['/students/view-student', id]);
        break;

      case 'stakeholder':
      case 'stakeholders':
        this.router.navigate(['/stakeholders/view-stakeholder', id]);
        break;

      case 'person':
        if (activity.relatedUser?.isStakeholder) {
          this.router.navigate(['/stakeholders/view-stakeholder', id]);
        } else {
          this.router.navigate(['/students/view-student', id]);
        }
        break;

      default:
        this.toastr.warning('Unknown related entity.');
    }
  }

  /**
   * * Apply Filter
   * ? This function is used for filtering the activities as per the selected filters and then show the chart on filtered data.
   */
  applyFilter() {

    this.totalPercentage = this.calculatePercentage();

    if (this.totalPercentage >= 70) {
      this.chartColor = 'success'
    }
    else if (this.totalPercentage >= 40 && this.totalPercentage < 70) {
      this.chartColor = 'warning'
    }
    else if (this.totalPercentage < 40) {
      this.chartColor = 'danger'
    }
    this.chartOptions = getChartOptions(this.chartHeight, this.chartColor, this.totalPercentage);
  }

  /**
   * * Get Total Members Id
   * ? This function is used to find out the total members involved in the activities.
   * @returns This function returns the total number of members.
   */
  getTotalMembersId() {
    this.organizatonService.getOrganizationsList().subscribe({
      next: (response => {
        this.membersSize = response?.data?.organizationsByDate?.items?.length;
      })
    });
  }

  /**
   * * Get Total User Id
   * ? This function is used to find out the total students and total stakeholders involved in the activities.
   */
  getTotalUserId() {
    this.sharedService.getUserList().subscribe(({ data }: any) => {
      let userData = data.userByDate.items;
      this.studentSize = userData.filter(
        (element: any) =>
          element.isStakeholder !== true &&
          !element._deleted
      ).length;
      this.stackSize = userData.filter(
        (element: any) =>
          (element.isStakeholder === true) &&
          !element._deleted
      ).length;
    });
  }

  /**
   * * Get Most Active Activity
   * ? This function is used to find out the most active entity(student/stakeholder/member).
   * @param activitiesArray This parameter holds the list of activities as per the current setting.
   * @returns This function returns the activity data of the most active activity.
   */
  getMostActiveActivity(activitiesArray: any[]): any {
    const activities = [...activitiesArray];

    if (activities.length === 0) {
      return null;
    }

    const activityCounts: Record<string, number> = {};
    activities.forEach((activity: any) => {
      const moduleId = activity.moduleId;
      if (activityCounts[moduleId]) {
        activityCounts[moduleId]++;
      } else {
        activityCounts[moduleId] = 1;
      }
    });
    let mostActiveActivity: any = null;
    let maxCount = 0;
    for (const moduleId in activityCounts) {
      if (activityCounts[moduleId] > maxCount) {
        maxCount = activityCounts[moduleId];
        mostActiveActivity = activities.find((activity: any) => activity.moduleId === moduleId);
      }
    }
    return mostActiveActivity;
  }

  /**
   * * Clear Filter
   * ? This function is responsible for clearing the set filters on the click of the clear button.
   */
  clearFilter() {
    this.initializeForm();
  }

  /**
 * * On Opened
 * ? This function is used for opening the UI of date picker.
 */
  onOpened(): void {
    this.ktMenuStaticUi = true;
  }

  /**
 * * On Closed
 * ? This function is used for closing the UI of date picker.
 */
  onClosed(): void {
    this.ktMenuStaticUi = false;
  }

  /**
   * * Get Dates
   * ? This function is used to generate start and end dates as per the input.
   * @param input This parameter holds the range name for generating dates.
   * @returns This function returns dates as per the inputs provided input.
   */
  getDates(input: any) {
    // Get today's date.
    const today = new Date();
    let date: any;
    // Switch on the input value.
    switch (input) {
      case "year":
        // Get the start and end dates of the year.
        date = [new Date(today.getFullYear(), 0, 1), new Date(today.getFullYear() + 1, 0, 0)];
        break;
      case "month":
        // Get the start and end dates of the month.
        date = [new Date(today.getFullYear(), today.getMonth(), 1), new Date(today.getFullYear(), today.getMonth() + 1, 0)];
        break;
      case "week":
        {
          // Get the start and end dates of the week.
          let startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - (today.getDay() || 7) + 1);
          let endDate = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate() + 6);
          date = [startDate, endDate];
          break;
        }
      default:
        // If the input value is not recognized, return an empty array.
        date = [];
        break;
    }

    return date;
  }


}

/**
 * * Get Chart Options
 * ? This function is used to generate chart-options for the goal-widget chart.
 * @param chartHeight This parameter holds the value for the height of the chart.
 * @param chartColor This parameter holds the value for the color of the chart.
 * @param totalPercentage This pararmeter holds the percent value to be displayed in the center of the chart.
 * @returns This function returns chart-options.
 */
function getChartOptions(chartHeight: string, chartColor: string, totalPercentage: number) {
  const lightColor = getCSSVariableValue('--kt-' + chartColor + '-light');
  const labelColor = getCSSVariableValue('--kt-gray-700');

  return {
    series: [isNaN(Number(totalPercentage)) ? 0 : totalPercentage],
    chart: {
      fontFamily: 'inherit',
      height: chartHeight,
      type: 'radialBar',
    },
    plotOptions: {
      radialBar: {
        hollow: {
          margin: 0,
          size: '65%',
        },
        dataLabels: {
          name: {
            show: false,
            fontWeight: '700',
          },
          value: {
            color: labelColor,
            fontSize: '30px',
            fontWeight: '700',
            offsetY: 12,
            show: true,
            formatter: function (val: any) {
              if (isNaN(Number(val))) {
                val = 0;
              }
              return val + '%'
            },
          },
        },
        track: {
          background: lightColor,
          strokeWidth: '100%',
        },
      },
    },
    colors: ["#354029"],
    stroke: {
      lineCap: 'round',
    },
    labels: ['Progress'],
  };
}
