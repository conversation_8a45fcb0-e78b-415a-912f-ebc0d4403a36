import { Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { forkJoin, lastValueFrom, map } from 'rxjs';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { Storage } from 'aws-amplify';
import { FormControl, Validators } from '@angular/forms';

import { SharedService } from '../../../../shared/services/shared.service';
import { PostsService } from '../../services/posts.service';
import { OrganizationsService } from 'src/app/pages/organizations/services/organizations.service';
import { BusinessesService } from 'src/app/pages/businesses/services/businesses.service';
import { UsersService } from 'src/app/pages/organizations/services/users.service';
import { PersonsService } from 'src/app/pages/persons/services/persons.service';
import { StakeholderKnowledgeService } from 'src/app/pages/persons/components/person-knowledge-repository/services/stakeholder-knowledge.service';
import { TranscribeService } from '../../services/transcribe.service';
import { TranscriptionRegenerationService } from 'src/app/shared/services/transcription-regeneration.service';
import { MvtWalletService } from 'src/app/shared/services/mvt-wallet.service';

@Component({
  selector: 'app-view-post',
  templateUrl: './view-post.component.html',
  styleUrls: ['./view-post.component.scss'],
})
export class ViewPostComponent implements OnInit {
  filters: any;
  postDetails: any;

  _version: number;
  currentPage: number;

  modal: string = '';
  postId: string = '';
  postTitle: string = '';
  currentTab: string = 'overview';
  updateGrade: number = 100.00;
  submissionUpdateStatus: string = 'APPROVED';
  organizationList: any = [];
  businessList: any = [];
  personsList: any = [];
  allUsers: any = [];
  stakeholderList: any = [];
  studentList: any = [];
  loggedInUserId: string = '';
  loggedInUserName: string = '';
  gradeOnUpdate: FormControl = new FormControl('100.00', [Validators.pattern(/^100(\.00?)?$|^\d{1,2}(\.\d{1,2})?$/)]);
  gradeFeedback: FormControl = new FormControl('');
  categoryList: any = {};
  organizationName: string;

  // Transcription-related properties
  summaryModalContent: any = null;
  currentSummaryFile: any = null;
  regeneratingTranscription: boolean = false;

  constructor(
    private readonly route: Router,
    private readonly location: Location,
    private readonly toastr: ToastrService,
    private readonly modalService: NgbModal,
    private readonly spinner: NgxSpinnerService,
    private readonly postsService: PostsService,
    private readonly activatedRoute: ActivatedRoute,
    public readonly sharedService: SharedService,
    private readonly organizationsService: OrganizationsService,
    private readonly businessService: BusinessesService,
    private readonly userService: UsersService,
    private readonly personsService: PersonsService,
    private readonly stakeholderKnowledgeService: StakeholderKnowledgeService,
    private readonly transcribeService: TranscribeService,
    private readonly transcriptionRegenerationService: TranscriptionRegenerationService,
    private readonly mvtWalletService: MvtWalletService
  ) { }

  ngOnInit(): void {
    this.getUsers();
    this.postId = this.activatedRoute.snapshot.paramMap.get('id') || '';

    if (this.postsService.filters) {
      this.filters = this.postsService.filters;
    }

    if (this.postsService.currentPage) {
      this.currentPage = this.postsService.currentPage;
    }

    this.getAllUsers();

    this.getPostDetails();
    this.sharedService.listCategories().pipe(
      map(response => response.data.listCategories.items),
      map(items =>
        items
          .filter((item: any) => !item._deleted)
          .map((item: any) => ({
            name: item.name.trim(),
            id: item.id
          }))
      )
    ).subscribe((response: any) => {
      response.forEach((item: any) => {
        this.categoryList[item.name] = item.id;
      });
    });
  }

  getOrganizationById(organizationId: string): void {
    this.organizationsService.getOrganizationById(organizationId)
      .subscribe({
        next: (res: any) => {
          this.organizationName = res?.data?.getOrganizations?.name;
        },
      });
  }

  getPostDetails() {
    this.sharedService.isLoading.next(true);

    forkJoin([
      this.postsService.getPostById(this.postId),
      this.sharedService.getCognitoUsersList(),
    ]).subscribe({
      next: (response: any) => {
        this.postDetails = response[0]?.data?.getSubmission;
        if (this.postDetails?._deleted) {
          this.toastr.warning('This story has been deleted!');
          this.location.back();
          return;
        }
        this.getOrganizationById(this.postDetails.memberId)

        this.postDetails.imagePreview = [];
        this.postDetails.videoPreview = [];
        this.postDetails.audioPreview = [];

        if (!(this.postDetails.images && this.postDetails.videos && this.postDetails.audios)) {
          this.spinner.hide();
          this.sharedService.isLoading.next(false);
        }

        if (this.postDetails?.images !== null) {
          this.postDetails?.images?.forEach((element: any, index: number) => {
            element = 'post/' + this.postId + '/' + 'images/' + element;
            Storage.get(element, {
              level: 'public',
            }).then((result: string) => {
              this.postDetails.imagePreview?.push(result);

              if (index === this.postDetails.images?.length - 1) {
                this.spinner.hide();

                this.sharedService.isLoading.next(false);
              }
            });
          });
        }

        if (this.postDetails?.videosThumbnail !== null) {
          if (this.postDetails?.videosThumbnail[0] !== '') {
            this.postDetails.videosThumbnail?.forEach((element: any, index: number) => {
              element = 'post/' + this.postId + '/' + 'videos-thumbnail/' + element;
              Storage.get(element, {
                level: 'public',
              }).then((result: string) => {
                this.postDetails.imagePreview?.push(result);
                if (index === this.postDetails.images?.length - 1) {
                  this.spinner.hide();

                  this.sharedService.isLoading.next(false);
                }
              });
            });
          }
        }

        if (this.postDetails?.videos !== null) {
          this.postDetails?.videos?.forEach((element: any, index: number) => {
            element = 'post/' + this.postId + '/' + 'videos/' + element;
            Storage.get(element, {
              level: 'public',
            }).then((result: string) => {

              this.postDetails.videoPreview?.push(result);

              if (index === this.postDetails.videos?.length - 1) {
                this.spinner.hide();

                this.sharedService.isLoading.next(false);
              }
            });
          });
        }

        if (this.postDetails?.audios !== null) {
          this.postDetails?.audios?.forEach((element: any, index: number) => {
            element = 'post/' + this.postId + '/' + 'audios/' + element;
            Storage.get(element, {
              level: 'public',
            }).then((result: string) => {
              this.postDetails.audioPreview?.push(result);
              if (index === this.postDetails.audios?.length - 1) {
                this.spinner.hide();
                this.sharedService.isLoading.next(false);
              }
            });
          });
        }
      },
      error: (error: any) => {
        this.location.back();
      },
    });
  }

  openImageCarouselModal(content: any, value: string) {
    this.modal = value;

    this.modalService.open(content, {
      size: 'md',
    });
  }

  openDeleteConfirmationModal(content: any, id: string, personName: string) {
    this.postId = id;
    this.postTitle = personName;

    this.modalService.open(content, {
      size: 'md',
    });
  }

  closeModal() {
    this.modalService.dismissAll();
  }

  updatePost() {
    const data = {
      id: this.postDetails.id,
      text: this.postDetails.text ?? 'Homework submission',
      location: this.postDetails.location,
      _version: this.postDetails._version,
      submissionStatus: this.postDetails.submissionStatus === 'false' ? 'true' : 'false',
    };

    this.postsService.updatePost(data).subscribe({
      next: () => {
        this.sharedService
          .generateLog({
            type: data.submissionStatus === 'false' ? 'DEACTIVATED' : 'ACTIVATED',
            moduleId: this.postDetails.id,
            moduleName: this.postDetails.text ?? 'Homework submission',
            moduleType: 'homework-submission',
            requestStatus: 'SYSTEM_APPROVED',
            activityType: "STORYTELLING",
            cityId: this.sharedService.defaultCityId.value
          })
          .subscribe();

        this.toastr.success(
          data.submissionStatus === 'true'
            ? 'Successfully activated the story!'
            : 'Successfully deactivated the story!'
        );

        this.postDetails.submissionStatus = data.submissionStatus;
      },
    });
  }

  deletePost() {
    const data = {
      id: this.postDetails.id,
      text: this.postDetails.text,
      location: this.postDetails.location,
      _version: this.postDetails._version,
      submissionStatus: '',
    };
    this.sharedService.isLoading.next(true);
    this.postsService.updatePost(data).subscribe({
      next: ({ data }: any) => {
        this.postsService
          .deletePost(this.postId, data.updateSubmission._version)
          .subscribe({
            next: ({ data }: any) => {
              if (this.postDetails.images && this.postDetails.images?.length > 0) {
                this.postDetails.images.forEach((element: string) =>
                  Storage.remove(element, {
                    level: 'public',
                  })
                );
              }
              if (this.postDetails.videos && this.postDetails.videos?.length > 0) {
                this.postDetails.videos.forEach((element: string) =>
                  Storage.remove(element, {
                    level: 'public',
                  })
                );
              }
              if (this.postDetails.memberId && this.postDetails.memberId.length > 0) {
                this.organizationsService
                  .getOrganizationById(this.postDetails?.memberId[0])
                  .subscribe((orgData: any) => {
                    this.sharedService
                      .generateLog({
                        type: 'DELETED',
                        moduleId: this.postDetails.id,
                        moduleName: data.DeleteSubmission.text ?? 'Homework submission',
                        moduleType: 'homework-submission',
                        relatedTo: 'member',
                        relatedId: this.postDetails?.memberId[0],
                        requestStatus: 'SYSTEM_APPROVED',
                        relatedName: orgData?.data?.getOrganizations?.name,
                        activityType: "STORYTELLING",
                        cityId: this.sharedService.defaultCityId.value
                      }).subscribe();
                  });
              }
              else {
                this.sharedService
                  .generateLog({
                    type: 'DELETED',
                    moduleId: this.postDetails.id,
                    moduleName: data.DeleteSubmission.text ?? 'Homework submission',
                    moduleType: 'homework-submission',
                    activityType: "STORYTELLING",
                    requestStatus: 'SYSTEM_APPROVED',
                    cityId: this.sharedService.defaultCityId.value
                  }).subscribe();
              }
              this.toastr.success('Successfully submission.');
              this.route.navigate(['/activity-submission']);
              this.closeModal();
            },
            error: (error: any) => {
            },
          });
      },
    });
  }

  /**
 * * Open Grade Modal
 * ? This function is used to trigger open the grade modal
 */
  openGradeModal(content: any) {
    this.modalService.open(content, {
      size: 'md',
    });
  }

  /**
   * * Submission Status Change
   * ? This function is called when the status checkbox is changed
   * @param status
   */
  submissionStatusChange(status: string) {
    this.submissionUpdateStatus = status;
  }

  /**
   * * Submit Grade
   * ? This function is used for changing the grade of the submission.
   */
  submitGrade() {
    this.sharedService.isLoading.next(true);
    this.spinner.show();
    let updateData = {
      id: this.postDetails?.id,
      grade: Number(this.gradeOnUpdate.value),
      submissionStatus: this.submissionUpdateStatus,
      feedback: this.gradeFeedback?.value,
      isDeleted: 'false',
      gradedBy: this.loggedInUserName,
      _version: this.postDetails?._version,
    }
    this.postsService.updatePost(updateData).subscribe({
      next: (res: any) => {
        const submission = res?.data?.updateSubmission;
        const homework = submission?.homework;
        const memberId = submission?.memberId;

        const homeworkMembers = this.findMatchingHomeworkMember(homework?.members?.items);
        const homeworkUsers = this.findMatchingHomeworkUser(homework?.studentsStakeholders?.items);

        this.updateStoriesMembers(memberId, 'UPDATE');

        if (submission.submissionStatus === 'APPROVED') {
          this.handleApprovedSubmission(submission, homeworkUsers, homeworkMembers);
        }

        if (submission.submissionStatus === 'DENIED') {
          this.handleDeniedSubmission(homeworkUsers, homeworkMembers);
        }
      }
    });


  }

  private findMatchingHomeworkMember(members: any[]): any {
    return members.find(member =>
      this.postDetails?.projectId === member.homeworkId &&
      this.postDetails?.memberId === member.memberId
    );
  }

  private findMatchingHomeworkUser(users: any[]): any {
    return users.find(user =>
      this.postDetails?.projectId === user.homeworkId &&
      this.postDetails?.memberId === user.studentStakeholderId
    );
  }

  private handleApprovedSubmission(submission: any, user: any, member: any): void {
    const updateStatus = {
      homeworkStatus: 'completed',
      homeworkCompletedDate: new Date().toISOString()
    };

    if (user?.id) {
      this.updateHomeworkUserOnApproved(user, updateStatus, 'user');
    } else if (member?.id) {
      this.updateHomeworkMemberOnApproved(member, updateStatus, 'member');
    }

    this.processKnowledgeEntry(submission);

    // Trigger transcription for approved submission
    this.triggerTranscriptionForApproval(submission);
  }

  private handleDeniedSubmission(user: any, member: any): void {
    const updateStatus = { homeworkStatus: 'denied' };

    if (user?.id) {
      this.updateHomeworkUserOnDenied(user, updateStatus, 'user');
    } else if (member?.id) {
      this.updateHomeworkMemberOnDenied(member, updateStatus, 'member');
    }
  }

  private updateHomeworkUserOnApproved(user: any, status: any, type: string): void {
    this.postsService.updateHomeworkUsers({ id: user.id, _version: user._version, ...status }).subscribe({
      next: (response: any) => {
        this.addSubmissionPoints(response, type);
      }
    });
  }

  private updateHomeworkUserOnDenied(user: any, status: any, type: string): void {
    this.postsService.updateHomeworkUsers({ id: user.id, _version: user._version, ...status }).subscribe({
      next: (response: any) => {
        this.handleDeniedActivities(response.data.updateHomeworkUsers.studentStakeholderId, response.data.updateHomeworkUsers.homeworkData.name, response.data.updateHomeworkUsers.studentStakeholderData);
      }
    });
  }

  private updateHomeworkMemberOnApproved(member: any, status: any, type: string): void {
    this.postsService.updateHomeworkOrganizations({ id: member.id, _version: member._version, ...status }).subscribe({
      next: (response: any) => {
        this.addSubmissionPoints(response, type);
      }
    });
  }

  private updateHomeworkMemberOnDenied(member: any, status: any, type: string): void {
    this.postsService.updateHomeworkOrganizations({ id: member.id, _version: member._version, ...status }).subscribe({
      next: (response: any) => {
        this.handleDeniedActivities(response.data.updateHomeworkOrganizations.memberId);
      }
    });
  }

  private handleDeniedActivities(memberId: string, homeworkName?: string, studentData?: any): void {
    this.postsService.getActivitiesList(this.postDetails?.id, memberId).subscribe((result: any) => {
      const activities = result?.data?.activitiesByDate?.items ?? [];
      for (const activity of activities) {
        this.sharedService.updateActivity({
          id: activity?.id,
          requestStatus: 'ADMIN_DENIED',
          _version: activity?._version
        }).subscribe();
      }

      this.toastr.success("The submission has been updated successfully.");
      this.closeModal();
      this.sharedService.isLoading.next(false);
      this.spinner.hide();
      location.reload();

      if (homeworkName && studentData) {
        this.sendMessage(homeworkName, 'deny', studentData);
      }
    });
  }

  private processKnowledgeEntry(submission: any): void {
    const hasVideo = submission.videos.length;
    const hasAudio = submission.audios.length;
    if (!hasVideo && !hasAudio) return;

    const fileUrl = hasVideo
      ? `post/${submission.id}/videos/${submission.videos[0]}`
      : `post/${submission.id}/audios/${submission.audios[0]}`;

    if (!submission?.homework?.microcredential?.categoryID) return;

    const knowledgeEntityType = ['organization', 'member'].includes(submission.projectType)
      ? 'ORGANIZATION'
      : 'STAKEHOLDER';

    const submissionData = {
      categoryId: submission.homework.microcredential.categoryID,
      subCategoryIds: [],
      fileUrl: fileUrl,
      fileType: hasVideo ? 'VIDEO' : 'AUDIO',
      knowledgeEntityType: knowledgeEntityType,
      name: submission.text,
      description: submission.description ?? '',
      durationInMinutes: submission.videoDuration ?? 0,
      submittedBy: submission.createdBy,
      ...(knowledgeEntityType === 'ORGANIZATION'
        ? { organizationId: submission.memberId }
        : { userId: submission.memberId }),
    };

    this.addToStakeholderKnowledge(submissionData);
    this.saveVideoOrAudioTranscript(submission.id, fileUrl);
  }

  addSubmissionPoints(response: any, type: any): void {
    let memberId: any, impactScore: number, entityType, microcredentialType, currentImpactScore: any, MVPTokens: any, membershipId: any, version: any;
    if (type === 'member') {
      memberId = response?.data?.updateHomeworkOrganizations?.memberId;
      impactScore = response?.data?.updateHomeworkOrganizations?.homeworkData?.assignmentPoints;
      entityType = 'member';
      microcredentialType = response?.data?.updateHomeworkOrganizations?.homeworkData?.microcredential?.type;
      MVPTokens = response?.data?.updateHomeworkOrganizations?.memberData?.membership?.MVPTokens;
      currentImpactScore = response?.data?.updateHomeworkOrganizations?.memberData?.membership?.currentImpactScore;
      membershipId = response?.data?.updateHomeworkOrganizations?.memberData?.membership?.id;
      version = response?.data?.updateHomeworkOrganizations?.memberData?.membership?._version
    } else {
      memberId = response?.data?.updateHomeworkUsers?.studentStakeholderId;
      impactScore = response?.data?.updateHomeworkUsers?.homeworkData?.assignmentPoints;
      entityType = response?.data?.updateHomeworkUsers?.studentStakeholderData?.isStakeholder ? 'stakeholder' : 'student';
      microcredentialType = response?.data?.updateHomeworkUsers?.homeworkData?.microcredential?.type;
      MVPTokens = response?.data?.updateHomeworkUsers?.studentStakeholderData?.membership?.MVPTokens;
      currentImpactScore = response?.data?.updateHomeworkUsers?.studentStakeholderData?.membership?.currentImpactScore;
      membershipId = response?.data?.updateHomeworkUsers?.studentStakeholderData?.membership?.id;
      version = response?.data?.updateHomeworkUsers?.studentStakeholderData?.membership?._version
    }
    this.sharedService.addPoints(
      {
        memberId: memberId,
        impactScore: impactScore,
        MVPTokens: impactScore,
        pointType: 'homework',
        status: 'CREDITED',
        type: entityType,
        cityId: this.sharedService.defaultCityId.value,
        createdBy: this.loggedInUserId,
        category: microcredentialType,
        categoryID: this.categoryList[microcredentialType?.trim()] ?? Object.values(this.categoryList)[0]
      },
    ).subscribe({
      next: (result => {
        this.transferMVT(memberId, impactScore);
        this.sharedService.updateMembership({
          id: membershipId,
          currentImpactScore: parseFloat(currentImpactScore) + impactScore,
          MVPTokens: parseFloat(MVPTokens) + (impactScore),
          lastAddedImpactScore: impactScore,
          _version: version,
        });
      })
    })
    //get activity by date
    this.postsService.getActivitiesList(this.postDetails?.id, memberId).subscribe((result: any) => {
      if (result?.data?.activitiesByDate?.items.length > 0) {
        for (const activity of result?.data?.activitiesByDate?.items ?? []) {
          const activityUpdate = {
            id: activity.id,
            requestStatus: 'ADMIN_APPROVED',
            _version: activity._version,
          };

          this.sharedService.updateActivity(activityUpdate).subscribe((resActivity: any) => {
            this.addMicrocredentialPoints(response, type)
          });

        }
      } else {
        this.toastr.success("The submission has been updated successfully.");
        this.closeModal();
        this.sharedService.isLoading.next(false);
        this.spinner.hide();
        location.reload();
      }

    })
  }
  addMicrocredentialPoints(response: any, type: any): void {
    let microcredentialId: any, memberId: any, microcredentialpoint: any, assignmentPoints: any, entityType: any, microcredentialType: any, membershipId: any, currentImpactScore: any, MVPTokens: any, version: any;
    if (type === 'member') {
      microcredentialId = response?.data?.updateHomeworkOrganizations?.homeworkData?.microcredential?.id;
      microcredentialpoint = response?.data?.updateHomeworkOrganizations?.homeworkData?.microcredential?.totalPoints;
      memberId = response?.data?.updateHomeworkOrganizations?.memberId;
      assignmentPoints = response?.data?.updateHomeworkOrganizations?.homeworkData?.assignmentPoints ?? 0;
      entityType = 'member';
      microcredentialType = response?.data?.updateHomeworkOrganizations?.homeworkData?.microcredential?.type;
      membershipId = response?.data?.updateHomeworkOrganizations?.memberData?.membership?.id;
      currentImpactScore = response?.data?.updateHomeworkOrganizations?.memberData?.membership?.currentImpactScore;
      MVPTokens = response?.data?.updateHomeworkOrganizations?.memberData?.membership?.MVPTokens;
      version = response?.data?.updateHomeworkOrganizations?.memberData?.membership?._version;

      this.postsService.getHomeworkMembersByMicrocredential(memberId, microcredentialId).subscribe({
        next: ((data: any) => {
          let homeworkUsersData = data.data.listHomeworkOrganizations.items.filter(
            (element: any) =>
              !element?._deleted && element?.homeworkStatus === "completed"
          );
          let completedHomeworkPoints = homeworkUsersData.reduce((prev: any, curr: any) => prev + Number(curr?.homeworkData?.assignmentPoints), 0);
          let oldCompletedHomeworkPoints = Number(completedHomeworkPoints) - Number(assignmentPoints);
          this.sendMessage(response?.data?.updateHomeworkOrganizations?.homeworkData?.name, 'approve', response?.data?.updateHomeworkOrganizations);
          if (microcredentialpoint > oldCompletedHomeworkPoints && microcredentialpoint <= completedHomeworkPoints) {
            this.sharedService.addPoints(
              {
                memberId: memberId,
                impactScore: microcredentialpoint,
                MVPTokens: microcredentialpoint,
                pointType: 'microcredential',
                status: 'CREDITED',
                type: entityType,
                cityId: this.sharedService.defaultCityId.value,
                createdBy: this.loggedInUserId,
                category: microcredentialType,
                categoryID: this.categoryList[microcredentialType?.trim()] ?? Object.values(this.categoryList)[0]
              },
            ).subscribe({
              next: (result => {
                this.transferMVT(memberId, microcredentialpoint);
                this.sharedService.updateMembership({
                  id: membershipId,
                  currentImpactScore: parseFloat(currentImpactScore) + microcredentialpoint,
                  MVPTokens: parseFloat(MVPTokens) + (microcredentialpoint),
                  lastAddedImpactScore: microcredentialpoint,
                  _version: version,
                });
                this.toastr.success("The submission has been updated successfully.");
                this.closeModal();
                this.sharedService.isLoading.next(false);
                this.spinner.hide();
                location.reload();
              })
            })
          }
        })
      })
    } else {
      microcredentialId = response?.data?.updateHomeworkUsers?.homeworkData?.microcredential?.id;
      microcredentialpoint = response?.data?.updateHomeworkUsers?.homeworkData?.microcredential?.totalPoints;
      memberId = response?.data?.updateHomeworkUsers?.studentStakeholderId;
      assignmentPoints = response?.data?.updateHomeworkUsers?.homeworkData?.assignmentPoints ?? 0;
      entityType = response?.data?.updateHomeworkUsers?.studentStakeholderData?.isStakeholder ? 'stakeholder' : 'student';
      microcredentialType = response?.data?.updateHomeworkUsers?.homeworkData?.microcredential?.type;
      membershipId = response?.data?.updateHomeworkUsers?.studentStakeholderData?.membership?.id;
      currentImpactScore = response?.data?.updateHomeworkUsers?.studentStakeholderData?.membership?.currentImpactScore;
      MVPTokens = response?.data?.updateHomeworkUsers?.studentStakeholderData?.membership?.MVPTokens;
      version = response?.data?.updateHomeworkUsers?.studentStakeholderData?.membership?._version

      this.postsService.getHomeworkUsersByMicrocredential(memberId, microcredentialId).subscribe({
        next: ((data: any) => {
          let homeworkUsersData = data.data.listHomeworkUsers.items.filter(
            (element: any) =>
              !element?._deleted && element?.homeworkStatus === "completed"
          );

          let completedHomeworkPoints = homeworkUsersData.reduce((prev: any, curr: any) => prev + Number(curr?.homeworkData?.assignmentPoints), 0);

          let oldCompletedHomeworkPoints = Number(completedHomeworkPoints) - Number(assignmentPoints);
          this.sendMessage(response?.data?.updateHomeworkUsers?.homeworkData?.name, 'approve', response?.data?.updateHomeworkUsers);
          if (microcredentialpoint > oldCompletedHomeworkPoints && microcredentialpoint <= completedHomeworkPoints) {
            this.sharedService.addPoints(
              {
                memberId: memberId,
                impactScore: Number(microcredentialpoint),
                MVPTokens: Number(microcredentialpoint),
                pointType: 'microcredential',
                status: 'CREDITED',
                type: entityType,
                cityId: this.sharedService.defaultCityId.value,
                createdBy: this.loggedInUserId,
                category: microcredentialType,
                categoryID: this.categoryList[microcredentialType?.trim()] ?? Object.values(this.categoryList)[0]
              },
            ).subscribe({
              next: (result => {
                this.transferMVT(memberId, microcredentialpoint);
                this.sharedService.updateMembership({
                  id: membershipId,
                  currentImpactScore: parseFloat(currentImpactScore) + microcredentialpoint,
                  MVPTokens: parseFloat(MVPTokens) + (microcredentialpoint),
                  lastAddedImpactScore: microcredentialpoint,
                  _version: version,
                });
                this.toastr.success("The submission has been updated successfully.");
                this.closeModal();
                this.sharedService.isLoading.next(false);
                this.spinner.hide();
                location.reload();
              })
            })
          }

        })
      });
    }
  }
  ///


  getOrganizations(): void {
    this.organizationsService.getOrganizationsList().subscribe((res: any) => {
      this.organizationList = res?.data?.organizationsByDate?.items;
      this.organizationList = this.organizationList.filter(
        (data: any) =>
          data !== null &&
          data?.cityId === this.sharedService.defaultCityId.value
      );
    });
  }
  getBusinessList() {
    this.sharedService.isLoading.next(false);
    this.businessService.getBusinessList().subscribe(({ data }) => {
      this.businessList = data.businessByDate.items.filter(
        (element: any) =>
          !element?._deleted &&
          this.sharedService.defaultCityId.value === element?.cityId
      );
      if (!this.postDetails?.id) {
        this.sharedService.isLoading.next(false);
        this.spinner.hide();
      }
    });
  }
  getPersonsList() {
    this.userService.getExistingUsers().subscribe({
      next: ({ data }: any) => {
        this.personsList = data?.userByDate?.items.filter(
          (element: any) =>
            element.id !== this.sharedService?.userData?.value['custom:dynamodbId'] &&
            element.cityId === this.sharedService?.defaultCityId?.value &&
            !element._deleted
        );
      },
      error: (error: any) => {
        this.location.back();
      },
    });
  }
  getAllUsers(): void {
    this.sharedService.getUserList(true).subscribe((data: any) => {
      this.allUsers = data?.data?.userByDate?.items;
      this.stakeholderList = data?.data?.userByDate?.items?.filter((stake: any) => stake.isStakeholder && stake?.isDeleted === 'false');
      this.studentList = data?.data?.userByDate?.items?.filter((student: any) => !student.isStakeholder && student.isDeleted === 'false');
    });
  }
  getUsers(): void {
    this.sharedService.getUserData().subscribe((res: any) => {
      this.loggedInUserId = res['custom:dynamodbId'];
      this.loggedInUserName = res?.name;
    });
  }

  sendMessage(memberName: any, subType: any, userData: any): void {
    let userList: any;
    let formData: any;

    if (subType === 'deny') {
      formData = {
        title:
          memberName + " homework has been denied by admin.",
        body: '',
        feedbackId: 'null',
        notificationType: 'homework-submission',
        taskNotificationsId: 'null',
        notificationIcon: 'notificationIcons/' + this.sharedService?.warningNotificationIconName,
      };
      userList = [{ endpointArn: userData?.endpointArn, id: userData?.id, isLogin: userData?.isLogin }]
      formData.userList = userList;
      this.sharedService.notificationSend(formData).subscribe((res: any) => { });
    } else {
      formData = {
        title:
          'You have received ' + (userData?.homeworkData?.assignmentPoints) + ' MVP tokens for submitting ' + memberName + ' assignment.',
        body: '',
        feedbackId: 'null',
        notificationType: 'points',
        taskNotificationsId: 'null',
        points: "100",
        MVPTokens: "25",
        notificationIcon: 'notificationIcons/' + this.sharedService?.mvpNotificationIconName,
      };

      userList = this.allUsers.filter((element: any) => element).map(({ endpointArn, id, isLogin }: any) => ({
        endpointArn,
        id,
        isLogin
      }));
      formData.userList = userList;
      this.sharedService.notificationSend(formData).subscribe(async (res: any) => {
        this.closeModal();
        this.sharedService.isLoading.next(false);
        await this.spinner.hide();
        location.reload();
      });
    }



  }

  updateStoriesMembers(
    memberId: any,
    storyOperation: any
  ): void {
    if (this.postDetails?.projectType === 'organization') {

      this.createStoryPoints({
        memberId: this.postDetails?.memberId,
        impactScore: this.sharedService?.pointForStoryAndProject,
        MVPTokens: parseFloat(this.sharedService?.pointForStoryAndProject),
        pointType: 'story',
        type: 'member',
        status: 'CREDITED',
        cityId: this.sharedService.defaultCityId.value,
        createdBy: this.loggedInUserId,
        category: this.postDetails?.categoryType,
        categoryID: this.categoryList[this.postDetails?.categoryType.trim()] ?? Object.values(this.categoryList)[0]
      });

      this.sharedService
        .getMember(memberId, this.postDetails?.projectType)
        .subscribe((res: any) => {
          let currentImpactScore =
            parseFloat(
              res?.data?.membershipByDate?.items[0].currentImpactScore
            ) + parseFloat(this.sharedService?.pointForStoryAndProject);
          let lastImpactScore = parseFloat(this.sharedService?.pointForStoryAndProject);
          let memberId = res?.data?.membershipByDate?.items[0].id;
          this.sharedService.updateMembership({
            id: memberId,
            cityId: res?.data?.membershipByDate?.items[0].cityId,
            organizationID:
              res?.data?.membershipByDate?.items[0].organizationID,
            type: 'member',
            name: res?.data?.membershipByDate?.items[0].name,
            shortDescription:
              res?.data?.membershipByDate?.items[0].shortDescription,
            imageUrl: res?.data?.membershipByDate?.items[0].imageUrl,
            currentImpactScore: currentImpactScore,
            MVPTokens: parseFloat(res?.data?.membershipByDate?.items[0].MVPTokens + (parseFloat(this.sharedService.pointForStoryAndProject))).toFixed(2),
            lastAddedImpactScore: lastImpactScore,
            _version: res?.data?.membershipByDate?.items[0]._version,
          });
        });

    }
    else if (this.postDetails?.projectType === 'person') {
      if (
        storyOperation === 'CREATE' ||
        (storyOperation === 'UPDATE' && this.postDetails.get('memberId')?.dirty)
      ) {
        this.createStoryPoints({
          memberId: this.postDetails?.memberId,
          impactScore: this.sharedService?.pointForStoryAndProject,
          MVPTokens: parseFloat(this.sharedService?.pointForStoryAndProject),
          pointType: 'story',
          status: 'CREDITED',
          type: 'student',
          cityId: this.sharedService.defaultCityId.value,
          createdBy: this.loggedInUserId,
          category: this.postDetails?.categoryType,
          categoryID: this.categoryList[this.postDetails?.categoryType.trim()] ?? Object.values(this.categoryList)[0]
        });

        this.sharedService.getMember(memberId, 'person').subscribe((res) => {
          if (res?.data?.membershipByDate?.items) {
            let currentImpactScore =
              Math.floor(
                res?.data?.membershipByDate?.items[0].currentImpactScore
              ) + parseFloat(this.sharedService?.pointForStoryAndProject);
            let membership = res?.data?.membershipByDate?.items[0];
            if (membership) {
              membership.currentImpactScore = currentImpactScore;
              membership.MVPTokens = parseFloat(res?.data?.membershipByDate?.items[0].MVPTokens + (parseFloat(this.sharedService.pointForStoryAndProject))).toFixed(2);
              membership.lastAddedImpactScore = parseFloat(this.sharedService?.pointForStoryAndProject);
              delete membership?.updatedAt;
              delete membership?._deleted;
              delete membership?._lastChangedAt;
              delete membership?.organization;
              delete membership?.person;
              this.personsService
                .editMembership({
                  ...membership,
                })
                .subscribe();
            }
          }
        });
      }
    }
  }

  createStoryPoints(storyElement: any): void {
    let selectedMemberName: any;
    if (this.postDetails?.projectType === 'organization') {
      const selectedItem = this.organizationList.find(
        (item: any) => item.id === storyElement?.memberId
      );
      selectedMemberName = selectedItem ? selectedItem.name : '';
    } else if (this.postDetails?.projectType === 'business') {
      const selectedItem = this.businessList.find(
        (item: any) => item.id === storyElement?.memberId
      );
      selectedMemberName = selectedItem ? selectedItem.name : '';
    } else {
      const selectedItem = this.personsList.find(
        (item: any) => item.id === storyElement?.memberId
      );
      selectedMemberName = selectedItem ? selectedItem.name : '';
    }
    this.sharedService.addPoints(storyElement).subscribe({
      next: (res: any) => {
        this.transferMVT(this.postDetails?.memberId, storyElement.impactScore);
        this.sendMessage(selectedMemberName, '', '');
      },
      error: (err: any) => { },
    });
  }

  private async addToStakeholderKnowledge(submissionData: any): Promise<void> {
    try {
      await this.stakeholderKnowledgeService.createKnowledgeRepositoryStore(submissionData);
      if (submissionData.knowledgeEntityType === "ORGANIZATION") {
        await this.stakeholderKnowledgeService.updateKnowledgeSummaryForOrganization(submissionData.organizationId, submissionData.categoryId, submissionData.durationInMinutes);
      } else {
        await this.stakeholderKnowledgeService.updateKnowledgeSummary(submissionData.userId, submissionData.categoryId, submissionData.durationInMinutes);
      }
    } catch (error) {
      console.error('Error adding to stakeholder knowledge:', error);
    }
  }

  private async saveVideoOrAudioTranscript(id: string, fileUrl: string) {
    try {
      await this.transcribeService.fetchVideoOrAudioTranscript(fileUrl, id, "submission");
    } catch (error) {
      console.error('Error adding to stakeholder knowledge:', error);
    }
  }

  // Method to fetch wallet address based on member ID
  async getWalletAddress(memberId: string): Promise<string> {
    try {
      // Get user details using personsService
      const response = await lastValueFrom(this.personsService.getUserById(memberId));
      const userData = response?.data?.getUser;

      // Return wallet address if available, otherwise use memberId as fallback
      if (userData?.walletAddress && userData.walletAddress.length > 0) {
        return userData.walletAddress;
      } else {
        return memberId; // Fallback to using memberId
      }
    } catch (error) {
      console.error("Error fetching wallet address:", error);
      return memberId; // Fallback to using memberId on error
    }
  }

  async transferMVT(memberId: string, amount: number): Promise<void> {
    try {

      this.mvtWalletService.adminTransferMVT(memberId, amount, `Admin transferred ${amount} MVT tokens for submission approval`).subscribe({
        next: (response: any) => {

        },
        error: (error: any) => {
          console.error("MVT transfer error:", error);
          const errorMessage = (error?.message ?? error?.error?.message) ?? 'Failed to transfer MVT tokens';
          console.error("Transfer error details:", errorMessage);
        }
      });
    } catch (error) {
      console.error("Error in MVT token transfer:", error);
    }
  }

  shouldShowTranscriptionIcon(): boolean {
    if (!this.postDetails) return false;

    const hasVideo = this.postDetails.videos && this.postDetails.videos.length > 0;
    const hasAudio = this.postDetails.audios && this.postDetails.audios.length > 0;

    const isApproved = this.postDetails.submissionStatus === 'APPROVED';

    return (hasVideo || hasAudio) && isApproved;
  }

  public showTranscription(modal: any): void {
    if (!this.shouldShowTranscriptionIcon()) return;

    this.currentSummaryFile = {
      id: this.postDetails.id,
      fileType: this.postDetails.videos?.length > 0 ? 'VIDEO' : 'AUDIO',
      fileUrl: this.getFileUrl()
    };

    this.summaryModalContent = this.postDetails.transcriptDetail ?? null;
    this.regeneratingTranscription = false;
    this.modalService.open(modal, { size: 'lg' });
  }

  private getFileUrl(): string {
    if (this.postDetails.videos?.length > 0) {
      return `post/${this.postDetails.id}/videos/${this.postDetails.videos[0]}`;
    } else if (this.postDetails.audios?.length > 0) {
      return `post/${this.postDetails.id}/audios/${this.postDetails.audios[0]}`;
    }
    return '';
  }

  private async triggerTranscriptionForApproval(submission: any): Promise<void> {
    try {
      const hasMedia = (submission.videos && submission.videos.length > 0) ||
        (submission.audios && submission.audios.length > 0);

      if (!hasMedia) {
        return;
      }

      if (submission.transcriptDetail) {
        return;
      }

      this.regeneratingTranscription = true;

      await this.postsService.triggerTranscriptionForApprovedSubmission(
        submission.id,
        submission.videos ?? [],
        submission.audios ?? []
      );

    } catch (error) {
      console.error('Error triggering transcription for approved submission:', error);
      this.toastr.error('Failed to start transcription process');
    } finally {
      this.regeneratingTranscription = false;
    }
  }

  public async regenerateTranscriptionForSubmission(): Promise<void> {
    if (!this.currentSummaryFile) return;
    await this.transcriptionRegenerationService.regenerateTranscription({
      file: this.currentSummaryFile,
      type: 'submission',
      transcribeFn: () => this.transcribeService.fetchVideoOrAudioTranscript(
        this.currentSummaryFile.fileUrl,
        this.currentSummaryFile.id,
        'submission'
      ),
      fetchUpdatedFn: async () => {
        this.getPostDetails();
        return this.postDetails?.transcriptDetail;
      },
      setLoading: (loading: boolean) => { this.regeneratingTranscription = loading; }
    });
  }

}
