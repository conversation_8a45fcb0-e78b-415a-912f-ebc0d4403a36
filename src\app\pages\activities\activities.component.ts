import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

import { finalize, forkJoin, map } from 'rxjs';

import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

import { FilterPipe } from './pipes/filter-pipe';
import { SharedService } from '../../shared/services/shared.service';
import { ActivitiesService } from './services/activities.service';
import { ActivitiesFilterComponent } from './components/activities-filter/activities-filter.component';
import { PostsService } from '../posts/services/posts.service';
import { PersonsService } from '../persons/services/persons.service';
import { OrganizationsService } from '../organizations/services/organizations.service';

@Component({
  selector: 'app-activities',
  templateUrl: './activities.component.html',
  styleUrls: ['./activities.component.scss'],
})
export class ActivitiesComponent implements OnInit {
  @ViewChild(ActivitiesFilterComponent)
  private readonly activitiesFilterComponent: ActivitiesFilterComponent;

  postId: string = '';
  exportType: string = '';
  showOptions: string = '';
  searchValue: string = '';
  currentTab: string = 'Card Panel';
  sortBy: string = 'Recently Updated';
  haveCommunityActivity: any = null;
  activityCategory: string = '';
  filters: any;
  activitiesList: any[] = [];
  organizationId: any;
  _version: number;
  activitiesCount: number = 0;
  tokensDate: any[] = [];
  personId: any;
  ktMenuStatic: string = 'false';
  loggedInUserId: string = '';
  loggedInUserName: string = '';
  userList: any = [];
  organizationList: any = [];
  cityData: any;

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly filterPipe: FilterPipe,
    private readonly spinner: NgxSpinnerService,
    public sharedService: SharedService,
    private readonly postService: PostsService,
    private readonly toastr: ToastrService,
    public activitiesService: ActivitiesService,
    private readonly personsService: PersonsService,
    private readonly modalService: NgbModal,
    private readonly organizationsService: OrganizationsService,
  ) { }

  ngOnInit(): void {
    this.sharedService.isLoading.next(true);
    this.spinner.show();
    this.getUserList();
    this.haveCommunityActivity =
      this.route.snapshot.paramMap.get('communityActivity');
    this.sharedService.getUserData().subscribe((res: any) => {
      this.loggedInUserName = res?.name;
      this.loggedInUserId = res['custom:dynamodbId'];
    });
    this.organizationId = this.route.snapshot.paramMap.get('organizationId');
    this.personId = this.route.snapshot.paramMap.get('personId');
    if (this.sharedService.getIsActivityClicked()) {
      this.getActivitiesList();
      this.sharedService.setIsActivityClicked(false);
      this.haveCommunityActivity = null;
    }

    if (this.organizationId) {
      this.getActivitiesList();
    } else if (this.personId) {
      this.getActivitiesPersonList();
    } else {
      this.getActivitiesList();
    }
    this.getAllCities();
  }

  /**
   * * Get Activity Categories
   * ? This function is used to get the activity categories that are emitted by the child component 'activities-chart-widget'.
   * @param eventValue This parameter holds the value containing the activity categories.
   */
  getActivityCategories(eventValue: any): void {
    // Use setTimeout to defer the update to the next change detection cycle
    setTimeout(() => {
      this.activityCategory = eventValue;
      this.cdr.detectChanges();
    });
  }

  /**
   * * Get Activities List
   * ? This function is used to fetch list of all activities.
   */
  getActivitiesList() {
    forkJoin([
      this.organizationId
        ? this.activitiesService.getActivitiesList(this.organizationId)
        : this.activitiesService.getActivitiesList(),
      this.sharedService.getCognitoUsersList(),
    ]).subscribe({
      next: (response: any) => {
        switch (this.haveCommunityActivity) {
          case 'membershipActivity': {

            this.activitiesList = response[0].data.activitiesByDate.items.filter(
              (element: any) =>
                (!element._deleted && element?.activityType === 'MEMBERSHIP' && element?.type !== 'TOKENS')
            );

            this.activitiesList.sort(
              (a, b) =>
                new Date(b.createdAt).getTime() -
                new Date(a.createdAt).getTime()
            );
            break;
          }
          case 'projectActivity': {
            this.activitiesList =
              response[0].data.activitiesByDate.items.filter(
                (element: any) =>
                  (!element._deleted && element?.type !== 'TOKENS') &&
                  element?.activityType === 'PROJECT'
              );
            break;
          }
          case 'storytellingActivity': {
            this.activitiesList =
              response[0].data.activitiesByDate.items.filter(
                (element: any) =>
                  (!element._deleted && element?.type !== 'TOKENS') &&
                  element?.activityType === 'STORYTELLING'
              );
            break;
          }
          case 'fundingActivity': {
            this.activitiesList =
              response[0].data.activitiesByDate.items.filter(
                (element: any) =>
                  (!element._deleted && element?.type !== 'TOKENS') &&
                  element?.activityType === 'FUNDING'
              );
            break;
          }
          case 'viewAll': {
            this.activitiesList =
              response[0].data.activitiesByDate.items.filter(
                (element: any) =>
                  (!element._deleted)
                  && (element?.relatedId === this.organizationId || element?.moduleId === this.organizationId) && (element?.moduleType === 'homework' || element?.moduleType === 'homework-submission')
              );
            break;
          }
          case 'depositActivity': {
            this.activitiesList =
              response[0].data.activitiesByDate.items.filter(
                (element: any) =>
                  (!element._deleted)
                  && (element.activityType === 'FUNDING' && !element.isRelationship && element?.type !== 'TOKENS')
              );
            break;
          }
          case 'disburseActivity': {
            this.activitiesList =
              response[0].data.activitiesByDate.items.filter(
                (element: any) =>
                  (!element._deleted)
                  && (element.activityType === 'FUNDING' && element.isRelationship && element?.type !== 'TOKENS')
              );
            break;
          }
          default: {
            if (this.haveCommunityActivity) {
              this.activitiesList =
                response[0].data.activitiesByDate.items.filter(
                  (element: any) => (!element._deleted && element?.type !== 'TOKENS')
                );
            } else {
              this.activitiesList = response[0].data.activitiesByDate.items;
            }
            break;
          }
        }

        let moduleType = ["student", "association", "stakeholder", "member", "update-profile",];
        let reletedType = ["student", "stakeholder", "member", "Organization", "Person", "Organizations", "person", "organization", "stakeholders"];
        let compareList: string[] = [];
        if (this.organizationList) {
          compareList = [...compareList, ...this.organizationList.map((organization: any) => organization?.id)];
        }
        if (this.userList) {
          compareList = [...compareList, ...this.userList.map((user: any) => user?.id)];
        }
        compareList = [...new Set(compareList)];

        this.activitiesList = this.activitiesList.filter((activity: any) => {
          if (moduleType.includes(activity.moduleType) || reletedType.includes(activity.relatedTo)) {
            if (compareList.includes(activity.moduleId) || compareList.includes(activity.relatedId)) {
              return activity;
            }
          } else {
            return activity;
          }
        })

        this.activitiesList = this.activitiesList.sort(
          (a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );

        // Set createdUserName with multiple fallback options
        this.activitiesList.forEach((activity: any) => {
          if (activity.type === 'TOKENS') return;
          
          // Debug logging for activities with missing usernames
          if (!activity.createdUserName) {
            console.log('Activity with missing username:', {
              id: activity.id,
              moduleType: activity.moduleType,
              moduleUser: activity.moduleUser,
              createdUserId: activity.createdUserId,
              activityType: activity.activityType
            });
          }
          
          // Try to set username with fallbacks
          activity.createdUserName = activity.createdUserName || 
                                   activity.moduleUser?.name || 
                                   activity.createdBy || 
                                   activity.actor?.name;
        });

        this.activitiesCount = this.activitiesList.length;
        this.spinner.hide();
        this.sharedService.isLoading.next(false);
        this.sortBy = 'Recently Updated';
        this.setToFilter('Recently Updated');
      },
      error: (error: any) => {
      },
    });
  }

  updateActivity(request: any, activity: any): void {
    this.sharedService.isLoading.next(true);
    this.spinner.show();

    const activityUpdate = {
      id: activity?.id,
      requestStatus: request,
      _version: activity?._version,
    };

    this.sharedService.updateActivity(activityUpdate).subscribe((resActivity: any) => {
      if (activity.moduleType === 'homework-submission') {
        this.handleHomeworkSubmission(request, activity);
      } else if (activity.moduleType === 'update-profile') {
        this.handleProfileUpdate(request, activity);
      } else {
        this.handleDefaultModuleType(request, activity, resActivity);
      }

      this.sharedService.isLoading.next(false);
      this.spinner.hide();
    });
  }


  private handleHomeworkSubmission(request: any, activity: any): void {
    const id = activity?.updatedData?.id;
    const status = request === 'ADMIN_APPROVED' ? 'completed' : 'denied';

    if (['stakeholder', 'student', 'person'].includes(activity.relatedTo)) {
      this.postService.getHomeworkUsersById(id).subscribe({
        next: (res: any) => {
          this.updateHomeworkUsers(res._version, id, status, request, activity, 'user');
        }
      });
    } else if (['member', 'organization'].includes(activity.relatedTo)) {
      this.postService.getHomeworkOrganizationsById(id).subscribe({
        next: (res: any) => {
          this.updateHomeworkUsers(res._version, id, status, request, activity, 'member');
        }
      });
    }
  }

  private updateHomeworkUsers(version: any, id: string, status: string, request: string, activity: any, type: string): void {
    const updateFn = type === 'user' ? this.postService.updateHomeworkUsers : this.postService.updateHomeworkOrganizations;

    updateFn.call(this.postService, {
      id,
      homeworkStatus: status,
      _version: version
    }).subscribe({
      next: (response: any) => {
        if (request === 'ADMIN_APPROVED') {
          this.addSubmissionPoints(response, type, activity);
          this.addMicrocredentialPoints(response, type);
        }

        this.postService.getSubmissionById(activity?.moduleId).subscribe({
          next: (result: any) => {
            this.updatePostStatus(result, activity, request);
          }
        });
      }
    });
  }

  private updatePostStatus(result: any, activity: any, request: string): void {
    const isActive = request === 'ADMIN_APPROVED' ? 'approved' : 'denied';
    this.postService.updatePost({
      id: activity?.moduleId,
      isActive,
      _version: result?.data?.getSubmission?._version
    }).subscribe({
      next: () => {
        activity.requestStatus = request;
      }
    });
  }

  private handleProfileUpdate(request: string, activity: any): void {
    if (activity?.updatedData?.walletAddress && request === 'ADMIN_APPROVED') {
      this.sharedService.updateWalletAddress(
        activity.updatedData.walletAddress,
        activity.moduleId
      ).subscribe({
        next: (response) => console.log('Wallet address updated successfully:', response),
        error: (error) => console.error('Error updating wallet address:', error)
      });
    }

    if (request === 'ADMIN_APPROVED') {
      this.approvedTask('Approved', activity);
    } else if (request === 'ADMIN_DENIED') {
      this.rejectTask('Denied', activity);
    }
  }

  private handleDefaultModuleType(request: string, activity: any, resActivity: any): void {
    this.sharedService.getAssociationData(activity).subscribe({
      next: (res: any) => {
        const item = res.data.associationsByDate.items[0];
        const input = {
          id: item?.id,
          status: true,
          _version: item?._version
        };

        if (resActivity?.data?.updateActivity?.requestStatus === 'ADMIN_APPROVED') {
          this.toastr.success('Activity Approved.!');
          activity.requestStatus = request;
          this.sharedService.updateAssociation(input).subscribe();
        } else {
          this.toastr.success('Activity Denied.!');
          activity.requestStatus = request;
          this.sharedService.deleteAssociation([item?.id]).subscribe();
        }
      }
    });
  }

  /**
   * * Get User List
   * ? This functionis used to fetch users and organizations both.
   */
  getUserList() {
    forkJoin([this.sharedService.getUserList(), this.organizationsService.getOrganizationsList()]).pipe(
      map((res) => {
        res[0]?.data?.userByDate?.items?.forEach((userData: any) => {
          if (userData?.phoneNumber) {
            userData.phoneNumber = userData.phoneNumber.replace(/-/g, '');
            userData.phoneNumber = userData.phoneNumber.replace('+1', '');
          }
        });
        return res;
      })
    ).subscribe((result: any) => {
      this.userList = result[0]?.data?.userByDate?.items;
      this.userList = this.userList.filter(
        (element: any) =>
          element.id !== this.sharedService.userData.value['custom:dynamodbId'] &&
          element.cityId === this.sharedService?.defaultCityId?.value &&
          !element._deleted
      );
      this.organizationList = result[1]?.data?.organizationsByDate?.items;
    })

  }


  /**
   * * Is User Exist
   * ? This function is used to check if the user exists or not.
   * @param cognitoUserObj This parameter holds the cognito user data object.
   * @param activityData This parameter holds the data of the activity in question.
   * @returns THis function returns true or false.
   */
  async isUserExist(cognitoUserObj: any, activityData: any) {
    let userExist = { userExist: false, mobile: 0, email: 0 };

    if (activityData?.moduleId) {


      this.userList.forEach((item: any) => {
        if (
          ((item.email as string).toLowerCase() ===
            cognitoUserObj.email.toLowerCase() ||
            item.phoneNumber === cognitoUserObj.phoneNumber) &&
          activityData?.moduleId != item.id
        ) {
          userExist.userExist = true;
          if ((item.email as string).toLowerCase() ===
            cognitoUserObj.email.toLowerCase()) {
            userExist.email = 1;
          }
          if (item.phoneNumber === cognitoUserObj.phoneNumber) {
            userExist.mobile = 1;
          }
          return userExist;
        }
      });
    } else {
      this.userList.forEach((item: any) => {
        if (
          (item.email as string).toLowerCase() ===
          cognitoUserObj.email.toLowerCase() ||
          '+1' + item.phoneNumber === cognitoUserObj.phoneNumber) {
          userExist.userExist = true;
          if ((item.email as string).toLowerCase() ===
            cognitoUserObj.email.toLowerCase()) {
            userExist.email = 1;
          }
          if (item.phoneNumber === cognitoUserObj.phoneNumber) {
            userExist.mobile = 1;
          }
          return userExist;
        }
      });
    }
    return userExist;
  }

  private buildCognitoUser(data: any, moduleId: string) {
    const cleanPhone = this.normalizePhone(data.phoneNumber);
    return {
      firstName: data.givenName,
      lastName: data.familyName,
      name: `${data.givenName} ${data.familyName}`,
      email: data.email,
      phoneNumber: cleanPhone,
      id: moduleId,
    };
  }

  private buildAppUser(moduleUser: any, data: any) {
    const cleanPhone = this.normalizePhone(data.phoneNumber);
    return {
      id: moduleUser.id,
      cognitoId: moduleUser.cognitoId,
      email: data.email?.toLowerCase(),
      givenName: data.givenName?.trim(),
      familyName: data.familyName?.trim(),
      userType: moduleUser.userType,
      name: `${data.givenName?.trim()} ${data.familyName?.trim()}`,
      phoneNumber: cleanPhone,
      streetAddressOne: moduleUser.streetAddressOne,
      streetAddressTwo: moduleUser.streetAddressTwo,
      city: moduleUser.city?.trim(),
      cityId: data.cityId,
      state: moduleUser.state,
      zipCode: moduleUser.zipCode,
      countryCode: moduleUser.countryCode,
      role: moduleUser.role,
      birthday: data.birthday,
      ethnicity: moduleUser.ethnicity ?? '',
      gender: data.gender,
      walletAddress: (data.walletAddress ?? moduleUser.walletAddress) ?? '',
      type: moduleUser.type,
      impactScore: moduleUser.impactScore,
      imageUrl: data.imageUrl,
      status: moduleUser.status,
      registeredFrom: moduleUser.registeredFrom,
      isStakeholder: moduleUser.isStakeholder,
      memberCode: moduleUser.memberCode,
      stackholderCities: moduleUser.stackholderCities,
      isDeleted: moduleUser.isDeleted,
      createdUserId: this.loggedInUserId,
      createdUserName: this.loggedInUserName,
    };
  }

  private normalizePhone(phone: string = ''): string {
    return phone.replace(/-/g, '').replace('+1', '');
  }

  private async checkUserExistence(cognitoUser: any, activityData: any): Promise<boolean> {
    const result = await this.isUserExist(cognitoUser, activityData);
    if (result.userExist) {
      if (result.email === 1 && result.mobile === 1) {
        this.toastr.error('Email and phone number already exist');
      } else if (result.mobile === 1) {
        this.toastr.error('Phone number already exists.');
      } else if (result.email === 1) {
        this.toastr.error('Email already exists');
      }
      return true;
    }
    return false;
  }

  private applyUpdatedFields(moduleUser: any, data: any) {
    const fields = ['givenName', 'familyName', 'email', 'cityId', 'birthday', 'gender', 'imageUrl'];
    fields.forEach(key => {
      if (data[key] != null) {
        moduleUser[key] = data[key];
      }
    });
    moduleUser.phoneNumber = this.normalizePhone(data.phoneNumber);
    if (data.walletAddress) {
      moduleUser.walletAddress = data.walletAddress;
    }
    moduleUser.name = `${moduleUser.givenName} ${moduleUser.familyName}`;
  }

  private updateCognito(cognitoUser: any, user: any) {
    return this.sharedService.editCognitoUserv2(user);
  }

  private finalizeApproval(status: any, activityData: any, userData: any) {
    // Clean up activityData
    ['createdBy', 'moduleUser', '_deleted', 'relatedUser', 'relatedMember', 'moduleMember'].forEach(
      key => delete activityData[key]
    );

    activityData.requestStatus = status === 'Approved' ? 'ADMIN_APPROVED' : 'ADMIN_DENIED';

    this.personsService.updateActivity({ id: activityData.id, ...activityData })
      .subscribe(() => {
        this.sharedService.generateLog({
          type: 'UPDATED',
          moduleId: userData.id,
          moduleName: userData.name,
          moduleType: userData.isStakeholder ? 'stakeholder' : 'student',
          requestStatus: 'SYSTEM_APPROVED',
          activityType: 'MEMBERSHIP',
          cityId: this.sharedService.defaultCityId.value,
        }).subscribe();
        this.sendMessage(activityData, userData, 'approve');
        this.toastr.success(
          activityData.updatedData?.walletAddress
            ? 'Profile updated successfully with new wallet address!'
            : 'Profile updated successfully!'
        );
      });
  }

  private handleError(error: any, data: any) {
    console.error('Error updating profile:', error);
    const message = data.walletAddress
      ? 'Failed to update wallet address. Please try again.'
      : 'Failed to update profile. Please try again.';
    this.toastr.error(message);
  }

  /**
   * * Approve Task
   * ? This function is called on the click of approve button and is used to set the status of the activity to given status.
   * @param status This parameter holds the status that is to be set.
   * @param activityData This parameter holds the data of the activity whose status is to be updated.
   * @returns If all checks are passed then it returns the activity data with updated status , else it returns false.
   */
  async approvedTask(status: any, activityData: any): Promise<void> {
    this.sharedService.isLoading.next(true);

    // Build and normalize objects
    const moduleUser = activityData.moduleUser;
    const updated = activityData.updatedData ?? {};
    const cognitoUser = this.buildCognitoUser(updated, activityData.moduleId);
    const user = this.buildAppUser(moduleUser, updated);

    // Check for existing Cognito user conflicts
    const exists = await this.checkUserExistence(cognitoUser, activityData);
    if (exists) {
      this.sharedService.isLoading.next(false);
      return;
    }

    // If email unchanged, remove from payload
    if (cognitoUser.email === moduleUser.email) {
      delete cognitoUser.email;
    }

    // Update moduleUser in-place
    this.applyUpdatedFields(moduleUser, updated);

    // Synchronize with Cognito
    this.updateCognito(cognitoUser, user)
      .pipe(finalize(() => this.sharedService.isLoading.next(false)))
      .subscribe({
        next: () => this.finalizeApproval(status, activityData, moduleUser),
        error: (error) => this.handleError(error, updated)
      });
  }

  /**
   * * Reject Task
   * ? This function is called on the click of reject button and is used to set the status of the activity to given status.
   * @param status This parameter holds the status that is to be set.
   * @param activityData This parameter holds the data of the activity whose status is to be updated.
   */
  rejectTask(status: any, activityData: any): void {
    let userData = activityData?.moduleUser;
    let notificationData = activityData;
    delete activityData?.createdBy;
    delete activityData?.moduleUser;
    delete activityData?._deleted;
    delete activityData?.relatedUser;
    delete activityData?.relatedMember;
    delete activityData?.moduleMember;

    if (status === 'Approved') {
      activityData.requestStatus = 'ADMIN_APPROVED';
    } else {
      activityData.requestStatus = 'ADMIN_DENIED';
    }

    this.personsService
      .updateActivity({ id: activityData.id, ...activityData })
      .subscribe({
        next: ({ data }) => {
          this.sharedService
            .generateLog({
              type: 'UPDATED',
              moduleId: activityData.moduleId,
              moduleName: activityData?.moduleName,
              moduleType: userData?.isStakeholder === true ? 'stakeholder' : 'student',
              requestStatus: 'SYSTEM_APPROVED',
              activityType: 'MEMBERSHIP',
              cityId: this.sharedService?.defaultCityId?.value,
            })
            .subscribe();
          this.sendMessage(notificationData, userData, 'deny');
          this.toastr.success('Request Denied.');
        },
        error: (error: any) => {
        },
      });
    this.closeModal();
  }

  /**
   * * Add Submission Points
   * ? This function is used for adding points as per the homework submitted.
   * @param response This parameter consists of the homework data.
   * @param type This parameter holds the type of entity performing the activity of submission.
   * @param activity This parameter holds the activity data of the submission action,
   */
  addSubmissionPoints(response: any, type: any, activity: any): void {
    let memberId: any, impactScore: number, entityType, coCreationType, currentImpactScore: any, MVPTokens: any, membershipId: any, version: any, memberName: any, homeworkName: any, homeworkId: any, imageUrl: any;
    if (type === 'member') {
      memberId = response?.data?.updateHomeworkOrganizations?.memberId;
      impactScore = response?.data?.updateHomeworkOrganizations?.homeworkData?.assignmentPoints;
      entityType = 'member';
      coCreationType = response?.data?.updateHomeworkOrganizations?.homeworkData?.coCreationType;
      MVPTokens = response?.data?.updateHomeworkOrganizations?.memberData?.membership?.MVPTokens;
      currentImpactScore = response?.data?.updateHomeworkOrganizations?.memberData?.membership?.currentImpactScore;
      membershipId = response?.data?.updateHomeworkOrganizations?.memberData?.membership?.id;
      memberName = response?.data?.updateHomeworkOrganizations?.memberData?.name;
      imageUrl = response?.data?.updateHomeworkOrganizations?.memberData?.imageUrl;
      version = response?.data?.updateHomeworkOrganizations?.memberData?.membership?._version
    } else {
      memberId = response?.data?.updateHomeworkUsers?.studentStakeholderId;
      impactScore = response?.data?.updateHomeworkUsers?.homeworkData?.assignmentPoints;
      entityType = response?.data?.updateHomeworkUsers?.studentStakeholderData?.isStakeholder ? 'stakeholder' : 'student';
      memberName = response?.data?.updateHomeworkUsers?.studentStakeholderData?.name;
      coCreationType = response?.data?.updateHomeworkUsers?.homeworkData?.coCreationType;
      MVPTokens = response?.data?.updateHomeworkUsers?.studentStakeholderData?.membership?.MVPTokens;
      currentImpactScore = response?.data?.updateHomeworkUsers?.studentStakeholderData?.membership?.currentImpactScore;
      membershipId = response?.data?.updateHomeworkUsers?.studentStakeholderData?.membership?.id;
      imageUrl = response?.data?.updateHomeworkUsers?.studentStakeholderData?.imageUrl;
      version = response?.data?.updateHomeworkUsers?.studentStakeholderData?.membership?._version
    }
    this.sharedService.addPoints(
      {
        memberId: memberId,
        impactScore: impactScore,
        MVPTokens: impactScore * this.sharedService?.tokenBaseValue,
        pointType: 'homework',
        status: 'CREDITED',
        type: entityType,
        cityId: this.sharedService.defaultCityId.value,
        createdBy: this.loggedInUserId,
        category: coCreationType,
      },
    ).subscribe({
      next: (result => {
        this.sharedService.updateMembership({
          id: membershipId,
          imageUrl: imageUrl,
          currentImpactScore: parseFloat(currentImpactScore) + impactScore,
          MVPTokens: parseFloat(MVPTokens) + (impactScore * this.sharedService?.tokenBaseValue),
          lastAddedImpactScore: impactScore,
          _version: version,
        });
      })
    })
    this.sharedService
      .generateLog({
        type: 'UPDATED',
        moduleId: activity.moduleId,
        moduleName: activity.moduleName,
        moduleType: 'homework-submission',
        relatedTo: entityType,
        relatedId: memberId,
        relatedName: memberName,
        activityType: 'MEMBERSHIP',
        cityId: this.sharedService.defaultCityId.value,
        activityTokens: impactScore.toString()
      })
      .subscribe((log) => {
      });
  }

  /**
   * * Get Persons Activities List
   * ? This function is used to fetch all the activities of person.
   */
  getActivitiesPersonList() {
    forkJoin([
      this.personId
        ? this.activitiesService.getActivitiesList(this.organizationId)
        : this.activitiesService.getActivitiesPersonsList(),
      this.sharedService.getCognitoUsersList(),
    ]).subscribe({
      next: (response: any) => {
        switch (this.haveCommunityActivity) {
          case 'membershipActivity': {
            this.activitiesList = response[0].data.activitiesByDate.items.filter(
              (element: any) =>
                (!element._deleted && element?.activityType === 'MEMBERSHIP' && element?.type !== 'TOKENS')
            );

            this.activitiesList.sort(
              (a, b) =>
                new Date(b.createdAt).getTime() -
                new Date(a.createdAt).getTime()
            );
            break;
          }
          case 'projectActivity': {
            this.activitiesList =
              response[0].data.activitiesByDate.items.filter(
                (element: any) =>
                  (!element._deleted && element?.type !== 'TOKENS') &&
                  element?.activityType === 'PROJECT'
              );
            break;
          }
          case 'storytellingActivity': {
            this.activitiesList =
              response[0].data.activitiesByDate.items.filter(
                (element: any) =>
                  (!element._deleted && element?.type !== 'TOKENS') &&
                  element?.activityType === 'STORYTELLING'
              );
            break;
          }
          case 'fundingActivity': {
            this.activitiesList =
              response[0].data.activitiesByDate.items.filter(
                (element: any) =>
                  (!element._deleted && element?.type !== 'TOKENS') &&
                  element?.activityType === 'FUNDING'
              );
            break;
          }
          case 'viewAll': {
            this.activitiesList =
              response[0].data.activitiesByDate.items.filter(
                (element: any) =>
                  (!element._deleted) && (element?.relatedId === this.personId || element?.moduleId === this.personId) && (element?.moduleType === 'homework' || element?.moduleType === 'homework-submission')
              );
            break;
          }
          default: {
            this.activitiesList =
              response[0].data.activitiesByDate.items.filter(
                (element: any) => !element._deleted
              );
            break;
          }
        }

        this.activitiesList.forEach((activityObj: any) => {
          if (activityObj.type !== 'TOKENS') {
            activityObj.createdUserName = response[1].data.users.find(
              (user: { userId: string; name: string }) =>
                user.userId === activityObj.createdUserId
            )?.name;
          }
        });

        this.activitiesCount = this.activitiesList.length;
        this.spinner.hide();
        this.sharedService.isLoading.next(false);
        this.sortBy = 'Recently Updated';
        this.setToFilter('Recently Updated');
      },
      error: (error: any) => {
      },
    });
  }


  setTokesDates(dateValues: any): void {
    this.tokensDate = dateValues;
  }

  /**
   * * Set To Filter
   * ? This function is used to set the date-range filter as per the parameter.
   * @param value This parameter holds the value for the date-range slot.
   * @param currentPage -
   */
  setToFilter(value: any, currentPage?: any): void {
    this.sortBy = value;

    this.activitiesCount = this.filterPipe.transform(
      this.activitiesList,
      this.filters,
      this.sortBy
    ).length;

    this.sharedService.currentPage.next(currentPage ?? 1);
  }

  /**
   * * Navigate To Organization
   * ? This function is used for redirecting visitor to member's profile page.
   * @param data This parameter holds the clicked member's data
   */
  navigateToOrganization(data: any): void {
    this.router.navigate([
      '/members/view-member',
      this.sharedService.getEncryptedId(data?.relatedId),
    ]);
  }


  /**
   * * Apply Filter
   * ? This function is used to get filters from child component 'activities-filter' and then call filter pipe to filter data according to the filters.
   * @param value This parameter holds the filter data. 
   */
  applyFilter(value: any, currentPage?: number) {
    this.filters = value;

    this.activitiesCount = this.filterPipe.transform(
      this.activitiesList,
      this.filters,
      this.sortBy
    ).length;

    this.sharedService.currentPage.next(currentPage || 1);
  }

  /**
   * * On Search
   * ? This function is used to call the apply filter function
   */
  onSearch() {
    this.activitiesFilterComponent.applyFilter();
  }

  /**
   * * Get Random Color
   * ? This function is used to return the color from the color-list using the index parameter.
   * @param index This parameter holds the index for fetching the color from color-list.
   * @returns This function returns a hex color code.
   */
  getRandomColor(index: number) {
    return this.sharedService.randomColor(index);
  }

  /**
   * * Profile Navigation
   * ? This function is used to redirect the visitor to the url in parameter.
   * @param activity  This parameter holds the current activity data.
   * @param url This parameter holds the url used for redirect.
   */
  profileNavigation(activity: any, url: string) {
    localStorage.setItem('actId', activity.id);
    this.router.navigate([url])
  }

  /**
   * * Get Current Page
   * ? This function returns the current page value (for pagination).
   */
  get currentPage() {
    return this.sharedService.currentPage.value;
  }

  /**
   * * Get Count Start Value
   * ? This function returns activity count start (for pagination).
   */
  get getCountStartValue() {
    return this.sharedService.getCountStartValue(this.activitiesCount);
  }

  /**
    * * Get Count End Value
    * ? This function returns activity count end (for pagination).
    */
  get getCountEndValue() {
    return this.sharedService.getCountEndValue(this.activitiesCount);
  }

  /**
   * * Go To Details Page
   * ? This function is used for redirecting to a particular submission of id given in parameter.
   * @param id This parameter holds the id of the submission.
   */
  goToDetailsPage(id: string) {
    this.router.navigate(['posts/view-post', id]);
  }

  /**
   * * Check Number Of Filters
   * ? This function returns the number of filters being applied by visitor.
   */
  get checkNumberOfFilters() {
    let count: number = 0;

    for (const key in this.filters) {
      if (key === 'types') {
        if (!this.filters[key].every((element: any) => !element.checked)) {
          count++;
        }
      }
    }

    return count;
  }

  /**
   * * Close Modal
   * ? This function is used for closing modal.
   */
  closeModal() {
    this.modalService.dismissAll();
  }

  /**
   * * Send Message
   * ? This function is used for sending message to mobile devices when any action is approved or denied.
   * @param activityData This parameter holds the data of the activity in question.
   * @param userData This parameter hold the data of the user doing the activity.
   * @param requestType This parameter holds the type of the request being propsed through the activity.
   */
  sendMessage(activityData: any, userData: any, requestType: any): void {
    let formData: any;
    if (requestType === 'deny') {
      formData = {
        title: activityData?.relatedName,
        body: 'Admin has denied your request.',
        notificationType: 'deny-update-profile',
        notificationIcon: 'notificationIcons/' + this.sharedService?.warningNotificationIconName,
      };
    } else {
      formData = {
        title: activityData?.relatedName,
        body: 'Admin has approved your request.',
        notificationType: 'approve-update-profile',
        notificationIcon: 'notificationIcons/' + this.sharedService?.feedbackNotificationiconName,
      };
    }

    formData.userList = [{ id: userData?.id, endpointArn: userData?.endpointArn, isLogin: userData?.isLogin }];

    this.sharedService.notificationSend(formData).subscribe((res: any) => { });
  }

  /**
   * * Get All Cities
   * ? This function is used for getting all city data.
   */
  getAllCities() {
    this.sharedService.getAllCities().subscribe({
      next: ((response: any) => {
        this.cityData = Object.fromEntries(response.data.listCities.items.map((city: any) => [city.id, city.name]));
      })
    })
  }

  /**
   * * Add Microcredential Points
   * ? This function is used to submit the earned points to the microcredential.
   * @param response API response for processing the point add procedure.
   * @param type This parameter accepts the type of entity connected to the microcredential in question.
   */
  addMicrocredentialPoints(response: any, type: any): void {
    let microcredentialId: any, memberId: any, microcredentialpoint: any, assignmentPoints: any, entityType: any, microcredentialType: any, membershipId: any, currentImpactScore: any, MVPTokens: any, version: any;
    if (type === 'member') {
      microcredentialId = response?.data?.updateHomeworkOrganizations?.homeworkData?.microcredential?.id;
      microcredentialpoint = response?.data?.updateHomeworkOrganizations?.homeworkData?.microcredential?.totalPoints;
      memberId = response?.data?.updateHomeworkOrganizations?.memberId;
      assignmentPoints = response?.data?.updateHomeworkOrganizations?.homeworkData?.assignmentPoints ?? 0;
      entityType = 'member';
      microcredentialType = response?.data?.updateHomeworkOrganizations?.homeworkData?.microcredential?.type;
      membershipId = response?.data?.updateHomeworkOrganizations?.memberData?.membership?.id;
      currentImpactScore = response?.data?.updateHomeworkOrganizations?.memberData?.membership?.currentImpactScore;
      MVPTokens = response?.data?.updateHomeworkOrganizations?.memberData?.membership?.MVPTokens;
      version = response?.data?.updateHomeworkOrganizations?.memberData?.membership?._version;

      this.postService.getHomeworkMembersByMicrocredential(memberId, microcredentialId).subscribe({
        next: ((data: any) => {
          let homeworkUsersData = data.data.listHomeworkOrganizations.items.filter(
            (element: any) =>
              !element?._deleted && element?.homeworkStatus === "completed"
          );
          let completedHomeworkPoints = homeworkUsersData.reduce((prev: any, curr: any) => prev + Number(curr?.homeworkData?.assignmentPoints), 0);
          let newCompletedHomeworkPoints = Number(completedHomeworkPoints) + Number(assignmentPoints);
          if (microcredentialpoint > completedHomeworkPoints && microcredentialpoint <= newCompletedHomeworkPoints) {
            this.sharedService.addPoints(
              {
                memberId: memberId,
                impactScore: microcredentialpoint,
                MVPTokens: microcredentialpoint * this.sharedService?.tokenBaseValue,
                pointType: 'microcredential',
                status: 'CREDITED',
                type: entityType,
                cityId: this.sharedService.defaultCityId.value,
                createdBy: this.loggedInUserId,
                category: microcredentialType,
              },
            ).subscribe({
              next: (result => {
                this.sharedService.updateMembership({
                  id: membershipId,
                  currentImpactScore: parseFloat(currentImpactScore) + microcredentialpoint,
                  MVPTokens: parseFloat(MVPTokens) + (microcredentialpoint * this.sharedService?.tokenBaseValue),
                  lastAddedImpactScore: microcredentialpoint,
                  _version: version,
                });
              })
            })
          }
        })
      })
    } else {
      microcredentialId = response?.data?.updateHomeworkUsers?.homeworkData?.microcredential?.id;
      microcredentialpoint = response?.data?.updateHomeworkUsers?.homeworkData?.microcredential?.totalPoints;
      memberId = response?.data?.updateHomeworkUsers?.studentStakeholderId;
      assignmentPoints = response?.data?.updateHomeworkUsers?.homeworkData?.assignmentPoints ?? 0;
      entityType = response?.data?.updateHomeworkUsers?.studentStakeholderData?.isStakeholder ? 'stakeholder' : 'student';
      microcredentialType = response?.data?.updateHomeworkUsers?.homeworkData?.microcredential?.type;
      membershipId = response?.data?.updateHomeworkUsers?.studentStakeholderData?.membership?.id;
      currentImpactScore = response?.data?.updateHomeworkUsers?.studentStakeholderData?.membership?.currentImpactScore;
      MVPTokens = response?.data?.updateHomeworkUsers?.studentStakeholderData?.membership?.MVPTokens;
      version = response?.data?.updateHomeworkUsers?.studentStakeholderData?.membership?._version

      this.postService.getHomeworkUsersByMicrocredential(memberId, microcredentialId).subscribe({
        next: ((data: any) => {
          let homeworkUsersData = data.data.listHomeworkUsers.items.filter(
            (element: any) =>
              !element?._deleted && element?.homeworkStatus === "completed"
          );
          let completedHomeworkPoints = homeworkUsersData.reduce((prev: any, curr: any) => prev + Number(curr?.homeworkData?.assignmentPoints), 0);

          let newCompletedHomeworkPoints = Number(completedHomeworkPoints) + Number(assignmentPoints);

          if (microcredentialpoint > completedHomeworkPoints && microcredentialpoint <= newCompletedHomeworkPoints) {
            this.sharedService.addPoints(
              {
                memberId: memberId,
                impactScore: microcredentialpoint,
                MVPTokens: microcredentialpoint * this.sharedService?.tokenBaseValue,
                pointType: 'microcredential',
                status: 'CREDITED',
                type: entityType,
                cityId: this.sharedService.defaultCityId.value,
                createdBy: this.loggedInUserId,
                category: microcredentialType,
              },
            ).subscribe({
              next: (result => {
                this.sharedService.updateMembership({
                  id: membershipId,
                  currentImpactScore: parseFloat(currentImpactScore) + microcredentialpoint,
                  MVPTokens: parseFloat(MVPTokens) + (microcredentialpoint * this.sharedService?.tokenBaseValue),
                  lastAddedImpactScore: microcredentialpoint,
                  _version: version,
                });
              })
            })
          }
        })
      });
    }
  }
}
