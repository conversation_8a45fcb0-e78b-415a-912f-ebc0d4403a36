/**
 * Centralized handler mapping service
 * Provides unified handler mapping for static and dynamic chatbot handlers
 * Eliminates duplication across chatbot.js and dynamicChatbot.js
 */

const { ChatTypeEnum } = require('../utils/constants');
const { createLogger } = require('../utils/logger');

const logger = createLogger('handlerMappingService');

// Lazy loading to avoid circular dependencies
let staticHandlers = null;
let dynamicHandlers = null;

/**
 * Lazy load static handlers to avoid circular dependencies
 * @returns {Object} Static handler functions
 */
const getStaticHandlers = () => {
  if (!staticHandlers) {
    try {
      staticHandlers = require('../../handlers/static');
    } catch (error) {
      logger.error('Failed to load static handlers', { error: error.message });
      staticHandlers = {};
    }
  }
  return staticHandlers;
};

/**
 * Lazy load dynamic handlers to avoid circular dependencies
 * @returns {Object} Dynamic handler functions
 */
const getDynamicHandlers = () => {
  if (!dynamicHandlers) {
    try {
      dynamicHandlers = require('../../handlers/dynamic');
    } catch (error) {
      logger.error('Failed to load dynamic handlers', { error: error.message });
      dynamicHandlers = {};
    }
  }
  return dynamicHandlers;
};

/**
 * Get static handler mapping
 * @returns {Object} Map of chat types to static handler functions
 */
const getStaticHandlerMap = () => {
  const handlers = getStaticHandlers();
  
  return {
    [ChatTypeEnum.COMMUNITY]: handlers.getCommunityEventsPrompt,
    [ChatTypeEnum.FAMILY]: handlers.getFamilyPrompt,
    [ChatTypeEnum.KNOWLEDGE]: handlers.getKnowledgePrompt,
    [ChatTypeEnum.UNIFY]: handlers.getUnifyPrompt,
  };
};

/**
 * Get dynamic handler mapping
 * @returns {Object} Map of chat types to dynamic handler functions
 */
const getDynamicHandlerMap = () => {
  const handlers = getDynamicHandlers();
  
  return {
    [ChatTypeEnum.COMMUNITY]: handlers.communityChatbot,
    [ChatTypeEnum.FAMILY]: handlers.familyChatbot,
    [ChatTypeEnum.KNOWLEDGE]: handlers.knowledgeChatbot,
    [ChatTypeEnum.UNIFY]: handlers.unifyChatbot,
  };
};

/**
 * Get static handler for a specific chat type
 * @param {string} chatType - The chat type
 * @returns {Function|null} The static handler function or null if not found
 */
const getStaticHandler = (chatType) => {
  const handlerMap = getStaticHandlerMap();
  const handler = handlerMap[chatType];
  
  if (!handler) {
    logger.warn(`No static handler found for chat type: ${chatType}`);
    return null;
  }
  
  if (typeof handler !== 'function') {
    logger.error(`Static handler for ${chatType} is not a function`, { 
      handlerType: typeof handler 
    });
    return null;
  }
  
  return handler;
};

/**
 * Get dynamic handler for a specific chat type
 * @param {string} chatType - The chat type
 * @returns {Function|null} The dynamic handler function or null if not found
 */
const getDynamicHandler = (chatType) => {
  const handlerMap = getDynamicHandlerMap();
  const handler = handlerMap[chatType];
  
  if (!handler) {
    logger.warn(`No dynamic handler found for chat type: ${chatType}`);
    return null;
  }
  
  if (typeof handler !== 'function') {
    logger.error(`Dynamic handler for ${chatType} is not a function`, { 
      handlerType: typeof handler 
    });
    return null;
  }
  
  return handler;
};

/**
 * Get all available chat types that have handlers
 * @returns {Array<string>} Array of available chat types
 */
const getAvailableChatTypes = () => {
  return Object.values(ChatTypeEnum);
};

/**
 * Check if a chat type has both static and dynamic handlers
 * @param {string} chatType - The chat type to check
 * @returns {Object} Object with hasStatic and hasDynamic boolean properties
 */
const checkHandlerAvailability = (chatType) => {
  const staticHandler = getStaticHandler(chatType);
  const dynamicHandler = getDynamicHandler(chatType);
  
  return {
    hasStatic: !!staticHandler,
    hasDynamic: !!dynamicHandler,
    chatType
  };
};

/**
 * Validate chat type
 * @param {string} chatType - The chat type to validate
 * @returns {boolean} True if valid, false otherwise
 */
const isValidChatType = (chatType) => {
  return Object.values(ChatTypeEnum).includes(chatType);
};

module.exports = {
  getStaticHandler,
  getDynamicHandler,
  getStaticHandlerMap,
  getDynamicHandlerMap,
  getAvailableChatTypes,
  checkHandlerAvailability,
  isValidChatType
};
