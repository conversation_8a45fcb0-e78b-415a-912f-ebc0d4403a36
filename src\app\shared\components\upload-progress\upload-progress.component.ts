import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-upload-progress',
  templateUrl: './upload-progress.component.html',
  styleUrls: ['./upload-progress.component.scss']
})
export class UploadProgressComponent {
  @Input() isUploading: boolean = false;
  @Input() currentUploadingFile: any;
  @Input() uploadProgress: number = 0;
  @Input() files: any[] = [];
  @Input() currentFileIndex: number = 0;
  @Input() inline: boolean = false; // New input to control inline vs overlay mode

 
}
