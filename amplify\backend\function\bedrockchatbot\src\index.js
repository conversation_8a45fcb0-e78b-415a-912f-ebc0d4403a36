/* Amplify Params - DO NOT EDIT
  API_MYVILLAGEPROJECTADMI_GRAPHQLAPIENDPOINTOUTPUT
  API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT
  AUTH_MYVILLAGEPROJECTADMIFEB4EA87_USERPOOLID
  ENV
  REGION
Amplify Params - DO NOT EDIT */

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */

const { ApolloError } = require('@apollo/client/core');
const AWS = require('aws-sdk');
const { createLogger, logSuccess, logError } = require('./shared/utils/logger');
const { generateRequestId, extractRequestContext } = require('./shared/utils/requestUtils');
const { createApolloClientFromEvent } = require('./shared/services/apolloClientService');

AWS.config.update({
  maxRetries: 3,
  httpOptions: { timeout: 30000, connectTimeout: 5000 },
  region: process.env.REGION,
  accessKeyId: process.env.ACCESS_KEY_ID,
  secretAccessKey: process.env.SECRET_ACCESS_KEY,
});

// Create a logger for this module
const logger = createLogger('index');

const { respondWithBedrockChatBot } = require('./services/bedrockchat.js');
const {
  storeChatbotPromptInDynamoDB,
  getChatbotAnswer,
  removeChatbotPromptFromDynamoDB,
} = require('./services/chatbot.js');

const graphqlEndpoint = process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIENDPOINTOUTPUT;



exports.handler = async (event, context, callback) => {
  const requestId = generateRequestId();
  const startTime = Date.now();
  
  // Create a child logger with request context
  const requestLogger = logger.child({
    functionName: 'bedrockchatbot',
    fieldName: event.fieldName,
    requestId,
    userId: event.identity?.sub || 'anonymous'
  });
  
  try {
    console.log(`[${requestId}] [START] Lambda execution started`);
    requestLogger.info({
      operation: 'route_request',
      hasArguments: !!event.arguments,
      arguments: event.arguments ? Object.keys(event.arguments) : []
    }, `Processing GraphQL request`);
    // Create Apollo Client using the shared service
    const apolloClient = createApolloClientFromEvent(event, context, graphqlEndpoint);

    requestLogger.debug('Apollo Client created', {
      endpoint: graphqlEndpoint,
      hasClient: !!apolloClient,
      requestId: context.awsRequestId,
      userId: event.identity?.sub || 'anonymous'
    });

    switch (event.fieldName) {
      case 'bedrockChatBot': {
        try {
          const { question, userId, chatType } = event.arguments;
          requestLogger.debug('Apollo Client initialized', { 
            isAvailable: !!apolloClient,
            hasQuery: !!(apolloClient?.query),
            endpoint: graphqlEndpoint
          });
          let chatBotPrompt = await getChatbotAnswer(question, userId, chatType, apolloClient);
          let bedRockChatResponse = await respondWithBedrockChatBot(chatBotPrompt);
          let assistantAnswer = bedRockChatResponse.data;
          if (!assistantAnswer || typeof assistantAnswer !== 'string' || !assistantAnswer.trim()) {
            assistantAnswer = 'Sorry, I was unable to generate a response at this time. Please try again later.';
          }
          if (bedRockChatResponse.success) {
            requestLogger.info('Successfully processed Bedrock chat request', { 
              responseLength: bedRockChatResponse?.data?.length || 0,
              truncatedResponse: bedRockChatResponse?.data?.substring(0, 100) + (bedRockChatResponse?.data?.length > 100 ? '...' : '')
            });
            return {
              success: true,
              message: bedRockChatResponse.message,
              User: question,
              Assistant: assistantAnswer
            };
          } else {
            requestLogger.error('Error in Bedrock chat response', { 
              error: bedRockChatResponse.message,
              code: bedRockChatResponse?.code,
              hasError: !!bedRockChatResponse?.error
            });
            return {
              success: false,
              message: bedRockChatResponse.message,
              error: (bedRockChatResponse?.error) ?? null,
              code: (bedRockChatResponse?.code) ?? null,
              User: question,
              Assistant: assistantAnswer
            };
          }
        } catch (error) {
          requestLogger.error('Unexpected error in bedrockChatBot handler', {
            error: error.message,
            stack: error.stack,
            ...(error.code && { code: error.code })
          });
          return {
            success: false,
            message: 'An error occurred while processing your request.',
            error: error.message,
            code: error.name || 'UnknownError',
            User: event.arguments?.question,
            Assistant: null,
          };
        }
      }
      case 'fetchChatbotData': {
        try {
          await storeChatbotPromptInDynamoDB(
            event.arguments.userId,
            event.arguments.chatType,
            apolloClient
          );
          requestLogger.info('Prompt data stored successfully', { fieldName: event.fieldName });
          return {
            message: 'Prompt data stored successfully',
          };
        } catch (error) {
          const errorTime = Date.now();
          const executionTime = errorTime - startTime;
          
          console.error(`[${requestId}] [ERROR] Handler failed after ${executionTime}ms`, {
            error: error.message,
            stack: error.stack
          });
          
          logError(requestLogger, error, 'handler', {
            operation: event.fieldName || 'unknown_operation',
            requestId,
            userId: event.identity?.sub || 'anonymous',
            executionTimeMs: executionTime,
            error: error.message,
            stack: error.stack
          });

          // Return a generic error message to the client
          return {
            statusCode: 500,
            body: JSON.stringify({ 
              error: 'An error occurred',
              requestId,
              timestamp: new Date().toISOString()
            }),
          };
        }
      }
      case 'removeChatbotData': {
        if (event.fieldName === 'getChatbotAnswer') {
          const { question, chatType } = event.arguments;
          const userId = event.identity?.sub;
          
          console.log(`[${requestId}] [FLOW] getChatbotAnswer invoked`, {
            chatType,
            questionLength: question?.length || 0
          });
          
          if (!userId) {
            const error = new Error('User not authenticated');
            console.error(`[${requestId}] [ERROR] Authentication failed`);
            throw error;
          }

          console.log(`[${requestId}] [FLOW] Calling getChatbotAnswer`);
          const result = await getChatbotAnswer(question, userId, chatType, apolloClient);
          console.log(`[${requestId}] [FLOW] getChatbotAnswer completed successfully`);
          return result;
        } else {
          try {
            const { userId, chatType } = event.arguments;
            
            // Validate required parameters
            if (!userId) {
              throw new Error('userId is required');
            }
            
            logger.info('Removing chatbot data', { 
              userId,
              chatType: chatType || 'ALL',
              operation: chatType ? 'deleteByChatType' : 'deleteAllForUser'
            });
            
            try {
              const result = await removeChatbotPromptFromDynamoDB(userId, chatType);
              
              logSuccess(logger, 'removeChatbotData_handler', { 
                userId,
                chatType: chatType || 'ALL',
                ...result
              }, { 
                fieldName: event.fieldName,
                operation: chatType ? 'deleteByChatType' : 'deleteAllForUser'
              });
              
              return {
                success: true,
                message: result.message || 'Operation completed successfully',
                count: result.count || 0,
                chatTypes: result.chatTypes || (chatType ? [chatType] : [])
              };
              
            } catch (error) {
              logError(logger, error, 'removeChatbotData_handler', { 
                fieldName: event.fieldName,
                arguments: event.arguments,
                operation: event.arguments?.chatType ? 'deleteByChatType' : 'deleteAllForUser'
              });
              
              // Return a more detailed error response
              const statusCode = error.statusCode || 500;
              const errorMessage = error.message || 'An error occurred while removing prompt data';
              
              return {
                success: false,
                error: errorMessage,
                statusCode,
                ...(error.code && { errorCode: error.code })
              };
            }
          } catch(error){
            console.log(error)
          }
        }
      }
      default:
        logger.error({ fieldName: event.fieldName }, 'Unknown field, unable to resolve');
        callback('Unknown field, unable to resolve' + event.fieldName, null);
        break;
    }
  } catch (error) {
    // Enhanced error logging
    const errorContext = {
      fieldName: event.fieldName,
      errorName: error.name,
      errorMessage: error.message,
      errorStack: error.stack,
      errorCode: error.extensions?.code || 'NO_ERROR_CODE',
      errorDetails: error.extensions || {},
      event: {
        type: event.type,
        arguments: event.arguments ? Object.keys(event.arguments) : [],
        identity: event.identity ? {
          sub: event.identity.sub,
          username: event.identity.username,
          groups: event.identity.groups
        } : null
      }
    };
    
    logError(logger, error, 'bedrockchatbot_handler', errorContext);
    
    // Log the full error to CloudWatch
    console.error('FULL ERROR DETAILS:', JSON.stringify({
      timestamp: new Date().toISOString(),
      requestId: context.awsRequestId,
      ...errorContext
    }, null, 2));
    
    throw new ApolloError(
      `Error processing ${event.fieldName}: ${error.message}`,
      error.extensions?.code || 'INTERNAL_SERVER_ERROR',
      {
        error: {
          ...error,
          message: error.message,
          stack: process.env.NODE_ENV === 'production' ? undefined : error.stack
        }
      }
    );
  }
};
