import { Injectable } from '@angular/core';
import { <PERSON> } from 'apollo-angular';
import {
  CREATE_ASSOCIATION_RELATION_TYPE,
  DELETE_ASSOCIATION_RELATION_TYPE,
  GET_ASSOCIATION_RELATION_TYPE,
  GET_ASSOCIATION_RELATION_TYPES,
  UPDATE_ASSOCIATION_RELATION_TYPE,
} from '../graphql/association-relation-type-graphql.queries';

@Injectable({
  providedIn: 'root',
})
export class AssociationRelationTypeService {
  constructor(private readonly apollo: Apollo) { }

  getAllAssociationRelationTypes() {
    return this.apollo.query({
      query: GET_ASSOCIATION_RELATION_TYPES,
      fetchPolicy: 'network-only',
    });
  }

  getAssociationRelationType(id: string) {
    return this.apollo.query({
      query: GET_ASSOCIATION_RELATION_TYPE,
      variables: { id },
    });
  }

  createAssociationRelationType(data: any) {
    return this.apollo.mutate({
      mutation: CREATE_ASSOCIATION_RELATION_TYPE,
      variables: { input: data },
    });
  }

  updateAssociationRelationType(data: any) {
    return this.apollo.mutate({
      mutation: UPDATE_ASSOCIATION_RELATION_TYPE,
      variables: { input: data },
    });
  }

  deleteAssociationRelationType(data: any) {
    return this.apollo.mutate({
      mutation: DELETE_ASSOCIATION_RELATION_TYPE,
      variables: { input: data },
    });
  }
} 