import { Component, Input } from '@angular/core';
import { SharedService } from '../../../../shared/services/shared.service';

@Component({
  selector: 'app-activity-avatar',
  templateUrl: './activity-avatar.component.html',
  styleUrls: ['./activity-avatar.component.scss']
})
export class ActivityAvatarComponent {
  @Input() name: string = '';
  @Input() index: number = 0;
  @Input() size: string = '40px';
  @Input() shape: string = 'circle'; // 'circle' or 'square'

  constructor(private sharedService: SharedService) {}

  get displayInitial(): string {
    return (this.name?.trim().charAt(0) || 'O').toUpperCase();
  }

  get colorClass(): string {
    const color = this.sharedService.randomColor(this.index);
    return `text-${color} bg-light-${color}`;
  }

  get sizeClass(): string {
    return `symbol-${this.size}`;
  }

  get shapeClass(): string {
    return `symbol-${this.shape}`;
  }
}
