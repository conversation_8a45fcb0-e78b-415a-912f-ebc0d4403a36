import { Injectable } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { Observable, of } from 'rxjs';
import { mergeMap, map } from 'rxjs/operators';
import { Apollo } from 'apollo-angular';

import {
  CREATE_POST,
  DELETE_POST,
  GET_ACTIVE_INACTIVE_POSTS,
  GET_ACTIVITIES,
  GET_HOMEWORK_ORGANIZATIONS,
  GET_HOMEWORK_USERS,
  GET_POST,
  GET_SUBMISSION_BY_ID,
  HOMEWORK_BY_DATE,
  LIST_HOMEWORK_ORGANIZATIONS,
  LIST_HOMEWORK_USERS,
  UPDATE_POST,
} from '../graphql/posts-graphql.queries';
import { SharedService } from 'src/app/shared/services/shared.service';
import { UPDATE_HOMEWORK, UPDATE_HOMEWORK_ORGANIZATIONS, UPDATE_HOMEWORK_USERS } from '../../homework/graphql/homework-graphql.queries';
import { environment } from 'src/environments/environment';
import { TranscribeService } from './transcribe.service';

@Injectable({
  providedIn: 'root',
})
export class PostsService {
  filters: any;

  currentPage: number;

  constructor(
    private readonly apollo: Apollo,
    readonly router: Router,
    private readonly shareService: SharedService,
    private readonly transcribeService: TranscribeService
  ) {
    router.events.subscribe((event: any) => {
      if (event instanceof NavigationEnd) {
        if (
          !(router.url.includes('view-person') || router.url === '/persons')
        ) {
          this.filters = null;
          this.currentPage = 1;
        }
      }
    });
  }

  getPostsList(): Observable<any> {
    let filterWithCity = { cityId: { eq: this.shareService.defaultCityId.value } };
    return this.apollo.query({
      query: GET_ACTIVE_INACTIVE_POSTS,
      variables: { filter: filterWithCity },
    });
  }

  getPostById(id: string): Observable<any> {
    return this.apollo.query({
      query: GET_POST,
      variables: { id },
    });
  }

  createPost(data: any): Observable<any> {
    return this.apollo.mutate({
      mutation: CREATE_POST,
      variables: {
        input: data,
      },
    });
  }

  updatePost(data: any): Observable<any> {
    return this.apollo.mutate({
      mutation: UPDATE_POST,
      variables: {
        input: data,
      },
    });
  }

  deletePost(postId: string, version: number): Observable<any> {
    return this.apollo.mutate({
      mutation: DELETE_POST,
      variables: {
        input: {
          id: postId,
          _version: version,
        },
      },
    });
  }

  getHomeworkUser(userId: string): Observable<any> {
    let filterWithUser = { studentStakeholderId: { eq: userId } };
    return this.apollo.query({
      query: LIST_HOMEWORK_USERS,
      variables: { filter: filterWithUser },
    });
  }

  getAllHomeworkUser(userId: string): Observable<any> {
    let filterWithUser = { studentStakeholderId: { eq: userId } };

    const fetchRecords = (nextToken?: string): Observable<any> => {
      return this.apollo.query({
        query: LIST_HOMEWORK_USERS,
        variables: {
          filter: filterWithUser,
          nextToken: nextToken
        },
      }).pipe(
        mergeMap((response: any) => {
          const items = response.data.listHomeworkUsers.items;
          const newNextToken = response.data.listHomeworkUsers.nextToken;

          if (newNextToken) {
            return fetchRecords(newNextToken).pipe(
              map((moreItems: any) => {
                return [...items, ...moreItems];
              })
            );
          }

          return of(items);
        })
      );
    };

    return fetchRecords().pipe(
      map(items => ({
        data: {
          listHomeworkUsers: {
            items: items
          }
        }
      }))
    );
  }

  getHomeworkUsersByMicrocredential(userId: string, microcredentialId: string): Observable<any> {
    let filterWithUser = { studentStakeholderId: { eq: userId }, programsHomeworkUsersId: { eq: microcredentialId } };
    return this.apollo.query({
      query: LIST_HOMEWORK_USERS,
      variables: { filter: filterWithUser },
    });
  }

  getHomeworkMember(organizationId: string): Observable<any> {
    let filterWithMember = { memberId: { eq: organizationId } };
    return this.apollo.query({
      query: LIST_HOMEWORK_ORGANIZATIONS,
      variables: { filter: filterWithMember },
    });
  }

  getAllHomeworkMember(organizationId: string): Observable<any> {
    let filterWithMember = { memberId: { eq: organizationId } };

    const fetchRecords = (nextToken?: string): Observable<any> => {
      return this.apollo.query({
        query: LIST_HOMEWORK_ORGANIZATIONS,
        variables: {
          filter: filterWithMember,
          nextToken: nextToken
        },
      }).pipe(
        mergeMap((response: any) => {
          const items = response.data.listHomeworkOrganizations.items;
          const newNextToken = response.data.listHomeworkOrganizations.nextToken;

          if (newNextToken) {
            return fetchRecords(newNextToken).pipe(
              map((moreItems: any) => {
                return [...items, ...moreItems];
              })
            );
          }

          return of(items);
        })
      );
    };

    return fetchRecords().pipe(
      map(items => ({
        data: {
          listHomeworkOrganizations: {
            items: items
          }
        }
      }))
    );
  }

  getHomeworkMembersByMicrocredential(organizationId: string, microcredentialId: string): Observable<any> {
    let filterWithMember = { memberId: { eq: organizationId }, programsHomeworkOrganizationsId: { eq: microcredentialId } };
    return this.apollo.query({
      query: LIST_HOMEWORK_ORGANIZATIONS,
      variables: { filter: filterWithMember },
    });
  }

  updateHomework(input: any): Observable<any> {
    return this.apollo.mutate({
      mutation: UPDATE_HOMEWORK,
      variables: {
        input
      },
    });
  }

  updateHomeworkUsers(input: any): Observable<any> {
    return this.apollo.mutate({
      mutation: UPDATE_HOMEWORK_USERS,
      variables: {
        input
      },
    });
  }
  updateHomeworkOrganizations(input: any): Observable<any> {
    return this.apollo.mutate({
      mutation: UPDATE_HOMEWORK_ORGANIZATIONS,
      variables: {
        input
      },
    });
  }

  getHomeworkUsersById(id: string): Observable<any> {
    return this.apollo.query({
      query: GET_HOMEWORK_USERS,
      variables: { id },
    });
  }

  getSubmissionById(id: string): Observable<any> {
    return this.apollo.query({
      query: GET_SUBMISSION_BY_ID,
      variables: { id },
    });
  }

  getHomeworkOrganizationsById(id: string): Observable<any> {
    return this.apollo.query({
      query: GET_HOMEWORK_ORGANIZATIONS,
      variables: { id },
    });
  }

  getHomeWorkList(organizationId: string, limit?: number): Observable<any> {
    let data: any;
    let filterWithCity = { cityId: { eq: this.shareService.defaultCityId.value } }
    if (organizationId) {
      data = {
        query: HOMEWORK_BY_DATE,
        variables: {
          orgId: organizationId,
          limit: limit || environment.recordsLimit
        },
      };
    } else {
      data = {
        query: HOMEWORK_BY_DATE,
        variables: {
          filter: filterWithCity,
          limit: limit || environment.recordsLimit
        },
      };
    }

    return this.apollo.query(data);
  }

  getActivitiesList(organizationId?: string, relatedId?: string, limit?: number): Observable<any> {

    let filterWithOrg = {
      moduleId: { eq: organizationId },
      relatedId: { eq: relatedId },
      requestStatus: { eq: 'PENDING_APPROVAL' }
    }
    let filterWithCity = { cityId: { eq: this.shareService.defaultCityId.value } };
    return this.apollo.query({
      query: GET_ACTIVITIES,
      variables: {
        filterInput: (organizationId && relatedId) ? filterWithOrg : filterWithCity,
        limit: limit || environment.recordsLimit,
      },
    });
  }

  async triggerTranscriptionForApprovedSubmission(submissionId: string, videos: string[], audios: string[]): Promise<void> {
    try {

      if (videos && videos.length > 0) {
        for (const video of videos) {
          const s3URI = `post/${submissionId}/videos/${video}`;
          await this.transcribeService.fetchVideoOrAudioTranscript(s3URI, submissionId, 'submission');
        }
      }

      if (audios && audios.length > 0) {
        for (const audio of audios) {
          const s3URI = `post/${submissionId}/audios/${audio}`;
          await this.transcribeService.fetchVideoOrAudioTranscript(s3URI, submissionId, 'submission');
        }
      }

    } catch (error) {
      console.error('Error during transcription process:', error);
      throw error;
    }
  }

}
