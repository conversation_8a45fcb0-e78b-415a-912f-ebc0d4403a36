import { Component, OnInit, OnDestroy } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { MvtWalletService } from 'src/app/shared/services/mvt-wallet.service';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { SwapRequest, SwapRequestStatus } from 'src/app/shared/types/transaction.types';

@Component({
  selector: 'app-mvt-swap-approval',
  templateUrl: './mvt-swap-approval.component.html',
  styleUrls: ['./mvt-swap-approval.component.scss']
})
export class MvtSwapApprovalComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();

  swapRequests: SwapRequest[] = [];
  displayRequests: SwapRequest[] = [];
  pendingRequests: SwapRequest[] = [];
  approvedRequests: SwapRequest[] = [];
  rejectedRequests: SwapRequest[] = [];
  failedRequests: SwapRequest[] = [];
  expiredRequests: SwapRequest[] = [];
  isLoading = false;
  processingRequestIds: Set<string> = new Set();
  spinnerMessage = '';
  SwapRequestStatus = SwapRequestStatus;
  activeTab: SwapRequestStatus = SwapRequestStatus.PENDING;

  constructor(
    private readonly mvtWalletService: MvtWalletService,
    private readonly toastr: ToastrService,
    private readonly router: Router
  ) { }

  ngOnInit(): void {
    this.loadSwapRequests();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadSwapRequests(): void {
    this.isLoading = true;
    this.spinnerMessage = 'Loading swap requests...';
    this.mvtWalletService.getMVTWalletSwapRequests(true) // isAdmin = true for admin panel
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: any) => {
          this.processSwapRequests(response);
          this.isLoading = false;
          this.spinnerMessage = '';
        },
        error: (error) => {
          this.handleSwapRequestError(error);
          this.isLoading = false;
          this.spinnerMessage = '';
        }
      });
  }

  private processSwapRequests(response: any): void {
    if (response?.data?.getMVTSwapRequests?.data) {
      const rawData = response.data.getMVTSwapRequests.data;
      const statusCounts = {};
      rawData.forEach((item: { status: string; }) => {
        const status = item.status ?? 'undefined';
        statusCounts[status] = (statusCounts[status] ?? 0) + 1;
      });

      this.swapRequests = rawData.map((item: any) => {
        let parsedDate: Date;
        try {
          // Use requestedAt from new API instead of date
          parsedDate = new Date(item.requestedAt ?? item.date);
          if (isNaN(parsedDate.getTime())) {
            parsedDate = new Date();
          }
        } catch (error) {
          console.warn('Error parsing date:', error);
          parsedDate = new Date();
        }

        let status: SwapRequestStatus;
        switch (item.status?.toUpperCase()) {
          case 'PENDING':
            status = SwapRequestStatus.PENDING;
            break;
          case 'PROCESSING':
            status = SwapRequestStatus.PROCESSING;
            break;
          case 'APPROVED':
          case 'COMPLETED': // New API uses COMPLETED instead of APPROVED
            status = SwapRequestStatus.APPROVED;
            break;
          case 'REJECTED':
            status = SwapRequestStatus.REJECTED;
            break;
          case 'FAILED':
            status = SwapRequestStatus.FAILED;
            break;
          case 'EXPIRED':
            status = SwapRequestStatus.EXPIRED;
            break;
          default:
            console.warn(`Unknown swap request status: ${item.status}, defaulting to PENDING`);
            status = SwapRequestStatus.PENDING;
        }

        return {
          id: item.id,
          date: parsedDate,
          userId: item.userId ?? item.user, // New API uses userId
          userName: item.userName ?? 'Unknown User',
          walletAddress: item.userWalletAddress ?? item.walletAddress, // New API uses userWalletAddress
          usdcAmount: parseFloat(item.usdcAmount) ?? 0,
          mvtAmount: parseFloat(item.mvtAmount) ?? 0,
          status: status,
          transactionHash: item.transactionHash ?? item.txHash ?? null
        };
      });
    } else {
      this.swapRequests = [];
    }

    this.filterRequests();
  }

  private filterRequests(): void {
    this.pendingRequests = this.swapRequests.filter(req =>
      req.status === SwapRequestStatus.PENDING || req.status === SwapRequestStatus.PROCESSING);

    this.approvedRequests = this.swapRequests.filter(req =>
      req.status === SwapRequestStatus.APPROVED);

    this.rejectedRequests = this.swapRequests.filter(req =>
      req.status === SwapRequestStatus.REJECTED);

    this.failedRequests = this.swapRequests.filter(req =>
      req.status === SwapRequestStatus.FAILED);

    this.expiredRequests = this.swapRequests.filter(req =>
      req.status === SwapRequestStatus.EXPIRED);

    switch (this.activeTab) {
      case SwapRequestStatus.PENDING:
        this.displayRequests = this.pendingRequests;
        break;
      case SwapRequestStatus.APPROVED:
        this.displayRequests = this.approvedRequests;
        break;
      case SwapRequestStatus.REJECTED:
        this.displayRequests = this.rejectedRequests;
        break;
      case SwapRequestStatus.FAILED:
        this.displayRequests = this.failedRequests;
        break;
      case SwapRequestStatus.EXPIRED:
        this.displayRequests = this.expiredRequests;
        break;
      default:
        this.displayRequests = this.pendingRequests;
    }
  }

  changeTab(tab: SwapRequestStatus): void {
    this.activeTab = tab;
    this.filterRequests();
  }

  refreshRequests(): void {
    this.loadSwapRequests();
  }

  approveRequest(request: SwapRequest): void {
    this.processingRequestIds.add(request.id);

    this.mvtWalletService.approveMVTWalletSwap(request.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: any) => {
          this.processApprovalResponse(response, request);
          this.processingRequestIds.delete(request.id);

          // Refresh the data to ensure UI consistency
          setTimeout(() => {
            this.loadSwapRequests();
          }, 1000);
        },
        error: (error) => {
          this.handleApprovalError(error);
          this.processingRequestIds.delete(request.id);
        }
      });
  }

  private processApprovalResponse(response: any, request: SwapRequest): void {

    if (response?.data?.approveMVTSwap) {
      const result = response.data.approveMVTSwap;

      if (result.statusCode === 200) {
        const index = this.swapRequests.findIndex(req => req.id === request.id);
        if (index !== -1) {
          // Backend returns COMPLETED status for successful approvals
          // Map to APPROVED for frontend consistency
          this.swapRequests[index].status = SwapRequestStatus.APPROVED;

          if (result.data?.txHash) {
            this.swapRequests[index].transactionHash = result.data.txHash;
          }

          this.filterRequests();
        } else {
          console.error(`Swap request ${request.id} not found in local array for status update`);
        }

        this.toastr.success(result.message ?? `Swap request for ${request.userName || 'user'} approved successfully`);
      } else {
        console.error('Approval failed with status code:', result.statusCode, 'Message:', result.message);
        this.toastr.error(result.message ?? 'Failed to approve swap request');
      }
    } else {
      console.error('Invalid approval response structure:', response);
      this.toastr.error('Invalid response received. Please try again.');
    }
  }

  rejectRequest(request: SwapRequest): void {
    this.processingRequestIds.add(request.id);

    this.mvtWalletService.rejectMVTWalletSwap(request.id, 'Request rejected by admin')
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: any) => {
          this.processRejectionResponse(response, request);
          this.processingRequestIds.delete(request.id);

          // Refresh the data to ensure UI consistency and transaction list updates
          setTimeout(() => {
            this.loadSwapRequests();
          }, 1000);
        },
        error: (error) => {
          console.error(`Rejection failed for ${request.id}:`, error);
          this.handleRejectionError(error);
          this.processingRequestIds.delete(request.id);
        }
      });
  }

  private processRejectionResponse(response: any, request: SwapRequest): void {

    if (response?.data?.rejectMVTSwap) {
      const result = response.data.rejectMVTSwap;

      if (result.statusCode === 200) {
        const index = this.swapRequests.findIndex(req => req.id === request.id);
        if (index !== -1) {
          this.swapRequests[index].status = SwapRequestStatus.REJECTED;
          this.filterRequests();
        } else {
          console.error(`Swap request ${request.id} not found in local array for status update`);
        }

        this.toastr.success(result.message ?? `Swap request for ${request.userName || 'user'} rejected successfully`);
      } else {
        console.error('Rejection failed with status code:', result.statusCode, 'Message:', result.message);
        this.toastr.error(result.message ?? 'Failed to reject swap request');
      }
    } else {
      console.error('Invalid rejection response structure:', response);
      this.toastr.error('Invalid response received. Please try again.');
    }
  }

  private handleSwapRequestError(error: any): void {
    console.error('Error fetching MVT swap requests:', error);
    this.swapRequests = [];
    this.filterRequests();
    this.toastr.error('Failed to fetch MVT swap requests. Please try again later.');
  }

  private handleApprovalError(error: any): void {

    let errorMessage = 'Failed to approve swap request. Please try again later.';

    // Extract more specific error messages
    if (error?.message) {
      errorMessage = error.message;
    } else if (error?.error?.message) {
      errorMessage = error.error.message;
    } else if (error?.graphQLErrors?.length > 0) {
      errorMessage = error.graphQLErrors[0].message;
    }

    this.toastr.error(errorMessage);
  }

  private handleRejectionError(error: any): void {
    this.toastr.error('Failed to reject swap request. Please try again later.');
  }

  formatDate(date: Date): string {
    if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
      return 'Invalid Date';
    }
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  truncateAddress(address: string): string {
    if (!address) return '';
    return address.substring(0, 6) + '...' + address.substring(address.length - 4);
  }

  navigateToDashboard(): void {
    this.router.navigate(['/funding-dashboard']);
  }

  openInEtherscan(transactionId: string): void {
    if (transactionId) {
      const etherscanUrl = `https://etherscan.io/tx/${transactionId}`;
      window.open(etherscanUrl, '_blank');
    }
  }

  isProcessingRequest(requestId: string): boolean {
    return this.processingRequestIds.has(requestId);
  }

  /**
   * ✅ ADDED: Retry failed swap request
   * @param request Failed swap request to retry
   */
  retryRequest(request: SwapRequest): void {
    // Show confirmation dialog
    const confirmRetry = confirm(
      `Are you sure you want to retry the failed swap request for ${request.userName}?\n\n` +
      `MVT Amount: ${request.mvtAmount}\n` +
      `USDC Amount: ${request.usdcAmount}\n\n` +
      `This will attempt to process the swap again.`
    );

    if (!confirmRetry) {
      return;
    }

    this.processingRequestIds.add(request.id);

    // Use the same approval method as for pending requests
    this.mvtWalletService.approveMVTWalletSwap(request.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: any) => {
          this.processApprovalResponse(response, request);
          this.processingRequestIds.delete(request.id);

          // Refresh the data to ensure UI consistency
          setTimeout(() => {
            this.loadSwapRequests();
          }, 1000);
        },
        error: (error) => {
          this.handleApprovalError(error);
          this.processingRequestIds.delete(request.id);
        }
      });
  }
}
