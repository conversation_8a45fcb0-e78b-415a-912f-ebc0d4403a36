import { Component, Input, OnInit } from '@angular/core';
import { NgxSpinnerService } from 'ngx-spinner';

import { getCSSVariableValue } from '../../../../_metronic/kt/_utils';
import { SharedService } from 'src/app/shared/services/shared.service';

@Component({
  selector: 'app-transactions-goal-widget',
  templateUrl: './transactions-goal-widget.component.html',
  styleUrls: ['./transactions-goal-widget.component.scss']
})
export class TransactionsGoalWidgetComponent implements OnInit {
  @Input() chartColor: string = '';
  @Input() chartHeight: string;
  chartOptions: any = {
    series: [],
    chart: {
      fontFamily: 'inherit',
      type: 'pie',
    },
    labels: ['Progress'],
  };
  cityFundLogs: any;
  cityFundTotal: any;
  memberList: any;
  debitedLogs: any;
  membershipData: any;

  constructor(
    private readonly sharedService: SharedService,
    private readonly spinner: NgxSpinnerService
  ) { }

  ngOnInit(): void {
    this.spinner.show('transaction_goal_chart_spinner');
    this.getAllTokens();
  }
  /**
   * * Copy Donate URL
   * ? This function is used for copying the donate url to clipboard.
   */
  copyDonateUrl() {
    const selBox = document.createElement('textarea');
    selBox.style.position = 'fixed';
    selBox.style.left = '0';
    selBox.style.top = '0';
    selBox.style.opacity = '0';
    selBox.value = 'http://donate.myvillageproject.com';
    document.body.appendChild(selBox);
    selBox.focus();
    selBox.select();
    navigator.clipboard.writeText(selBox.value).then(() => {
    }).catch(err => {
      console.error('Error in copying text: ', err);
    });
    document.body.removeChild(selBox);
  }

  /**
   * * Get All Tokens
   * ? This function is used for fetching the logs of token transfers
   */
  getAllTokens() {
    this.sharedService.getCityFundTransactions(this.sharedService.defaultCityId.value).subscribe({
      next: ((response: any) => {
        this.cityFundTotal = response?.data?.cityFundTransactionsByDate?.items.reduce((prev: any, curr: any) => prev + (curr?.amountStatus === 'DEBITED' ? -Math.abs(curr?.amount) : Math.abs(curr?.amount)), 0);
        this.getAllMembershipData();
      })
    })
  }

  /**
   * * Get All Membership Data
   * ? This function is used for fetching data of all members.
   */
  getAllMembershipData() {
    this.sharedService.getAllMembers().subscribe((response: any) => {
      this.membershipData = response?.data?.membershipByDate?.items.reduce((prev: any, curr: any) => {
        if (curr?.cityId === this.sharedService.defaultCityId.value) {
          return prev + Number(curr?.fundTokens)
        }
      }, 0);
      this.chartOptions = this.getChartOptions(this.chartHeight, this.chartColor);
      this.spinner.hide('transaction_goal_chart_spinner');
    })
  }

  /**
   * * Get Chart Options
   * ? This function is responsible for generating chart-options from API data.
   * @param chartHeight This parameter accepts the value of height of the chart. 
   * @param chartColor This parameter accepts the value of color of the chart.
   * @returns This function returns chart options.
   */
  getChartOptions(chartHeight: string, chartColor: string) {
    const lightColor = getCSSVariableValue('--kt-' + chartColor + '-light');
    const labelColor = getCSSVariableValue('--kt-gray-700');
    return {
      series: [(this.membershipData / this.cityFundTotal).toFixed(2)],
      chart: {
        fontFamily: 'inherit',
        height: chartHeight,
        type: 'radialBar',
      },
      plotOptions: {
        radialBar: {
          hollow: {
            margin: 0,
            size: '65%',
          },
          dataLabels: {
            name: {
              show: false,
              fontWeight: '700',
            },
            value: {
              color: labelColor,
              fontSize: '30px',
              fontWeight: '700',
              offsetY: 12,
              show: true,
              formatter: function (val: number) {
                if (isNaN(Number(val))) {
                  val = 0;
                }
                return val + '%';
              },
            },
          },
          track: {
            background: lightColor,
            strokeWidth: '100%',
          },
        },
      },
      colors: ['#826d59'],
      stroke: {
        lineCap: 'round',
      },
      labels: ['Progress'],
    };
  }
}

