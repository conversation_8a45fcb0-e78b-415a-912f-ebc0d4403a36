<div [ngClass]="metaClasses" *ngIf="activity">
  <ng-container *ngIf="layout === 'inline'">
    by <strong> {{ createdUserName | titlecase }}</strong>
    <span *ngIf="showTimestamp && createdAt">
      at {{ createdAt | date: timeFormat }}
    </span>
  </ng-container>
  
  <ng-container *ngIf="layout === 'block'">
    By <strong> {{ createdUserName }} </strong>
    <div *ngIf="showTimestamp && createdAt" class="mt-1">
      {{ createdAt | date: timestampFormat }} at {{ createdAt | date: timeFormat }}
    </div>
  </ng-container>
</div>
