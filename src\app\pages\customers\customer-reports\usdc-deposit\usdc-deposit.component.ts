import { Component, OnInit } from '@angular/core';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { SharedService } from 'src/app/shared/services/shared.service';
import { MvtWalletService } from 'src/app/shared/services/mvt-wallet.service';
import { Router } from '@angular/router';

interface USDCTransaction {
  id: string,
  date: Date,
  amount: number,
  status: string,
  from?: string,
  to?: string
}

@Component({
  selector: 'app-usdc-deposit',
  templateUrl: './usdc-deposit.component.html',
  styleUrls: ['./usdc-deposit.component.scss']
})
export class UsdcDepositComponent implements OnInit {
  math = Math;
  // Updated to use liquidity pool data
  usdcTotalReserves: number = 0;
  usdcAvailableBalance: number = 0;
  usdcAdminWalletAddress: string = '';
  usdcLastUpdated: string = '';
  // Keep usdcBalance for backward compatibility in UI
  usdcBalance: number = 0;

  depositAmount: number = 0;
  swapAmount: number = 0;
  exchangeRate: number = 1;
  walletAddress: string = '0x12345678987';
  contractAddress: string = '';
  isDepositing: boolean = false;
  isRequestingSwap: boolean = false;
  spinnerMessage: string = '';

  transactions: USDCTransaction[] = [];
  displayTransactions: any[] = [];
  isLoading: boolean = false;

  etherscanBaseUrl: string = 'https://sepolia.etherscan.io/tx/';

  private readonly ZERO_ADDRESS = '******************************************';

  constructor(
    private readonly sharedService: SharedService,
    private readonly mvtWalletService: MvtWalletService,
    private readonly toastr: ToastrService,
    private readonly spinner: NgxSpinnerService,
    private readonly router: Router
  ) { }

  ngOnInit(): void {
    this.getUSDCBalance();
    this.getUSDCTransactionList();
    this.getExchangeRate();
  }

  viewAllTransactions(): void {
    // Navigate to transactions view if implemented
    // this.router.navigate(['/funding-dashboard/usdc-transactions']);
  }

  openInEtherscan(transactionHash: string | undefined) {
    if (!transactionHash) {
      console.warn('No transaction hash available for Etherscan link');
      return;
    }
    const url = this.etherscanBaseUrl + transactionHash;
    window.open(url, '_blank');
  }

  formatAddress(address: string): string {
    if (!address) return '';

    if (address === this.ZERO_ADDRESS ||
      (this.contractAddress && address.toLowerCase() === this.contractAddress.toLowerCase())) {
      return 'Admin';
    }

    return address;
  }

  getTransactionDetails(transaction: USDCTransaction): string {
    return `From: ${this.formatAddress(transaction.from)}`;
  }

  getUSDCTransactionList() {
    this.isLoading = true;
    this.spinnerMessage = 'Loading transactions...';
    this.spinner.show('usdc_deposit_spinner');

    // Sample data for now - in production, replace with actual API call
    setTimeout(() => {

      this.displayTransactions = this.transactions;
      this.isLoading = false;
      this.spinnerMessage = '';
      this.spinner.hide('usdc_deposit_spinner');
    }, 1000);
  }

  getUSDCBalance(): void {
    this.spinnerMessage = 'Loading USDC liquidity pool data...';
    this.spinner.show('usdc_deposit_spinner');

    this.mvtWalletService.getUSDCLiquidityPool().subscribe({
      next: (response: any) => {
        if (response?.data?.getUSDCLiquidityPool?.statusCode === 200) {
          const poolData = response.data.getUSDCLiquidityPool.data;
          this.usdcTotalReserves = poolData.totalReserves || 0;
          this.usdcAvailableBalance = poolData.availableBalance || 0;
          this.usdcAdminWalletAddress = poolData.adminWalletAddress || '';
          this.usdcLastUpdated = poolData.lastUpdated || '';

          // Use totalReserves as the main balance for backward compatibility
          this.usdcBalance = this.usdcTotalReserves;
          this.contractAddress = this.usdcAdminWalletAddress;
          
        } else {
          console.error('Failed to fetch USDC liquidity pool data:', response?.data?.getUSDCLiquidityPool?.message);
          this.toastr.error('Failed to fetch USDC liquidity pool data');
          this.usdcBalance = 0;
        }
        this.spinnerMessage = '';
        this.spinner.hide('usdc_deposit_spinner');
      },
      error: (error: any) => {
        console.error('Error fetching USDC liquidity pool data:', error);
        // Fallback to mock data for testing
        this.usdcBalance = 150;
        this.usdcTotalReserves = 150;
        this.usdcAvailableBalance = 150;
        this.spinnerMessage = '';
        this.spinner.hide('usdc_deposit_spinner');
        this.toastr.error('Failed to fetch USDC liquidity pool data');
      }
    });
  }

  depositUSDC(): void {
    if (!this.depositAmount || this.depositAmount <= 0) {
      this.toastr.warning('Please enter a valid amount');
      return;
    }

    this.isDepositing = true;
    this.spinnerMessage = `Depositing ${this.depositAmount} USDC to your wallet...`;
    this.spinner.show('usdc_deposit_spinner');

    this.sharedService.depositUSDC(this.depositAmount).subscribe({
      next: (response: any) => {
        if (response?.data?.depositUSDC) {
          const depositResponse = response.data.depositUSDC;
          if (depositResponse.statusCode === 200) {
            this.toastr.success(depositResponse.message ?? 'USDC deposited successfully');
            this.depositAmount = 0;
            this.getUSDCBalance();
          } else {
            this.toastr.error(depositResponse.message ?? 'Failed to deposit USDC');
          }
        }

        this.isDepositing = false;
        this.spinnerMessage = '';
        this.getUSDCTransactionList();
        this.spinner.hide('usdc_deposit_spinner');
      },
      error: (error: any) => {
        console.error('Error depositing USDC:', error);

        this.isDepositing = false;
        this.spinnerMessage = '';
        this.getUSDCTransactionList();
        this.spinner.hide('usdc_deposit_spinner');
        this.toastr.error('Failed to deposit USDC');
      }
    });
  }

  formatDate(date: Date): string {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  getExchangeRate(): void {
    this.sharedService.getExchangeRate().subscribe({
      next: (response: any) => {
        if (response?.data?.getExchangeRate?.data) {
          this.exchangeRate = response.data.getExchangeRate.data.rate;
        }
      },
      error: (error: any) => {
        console.error('Error fetching exchange rate:', error);
      }
    });
  }

  calculateMVTAmount(): number {
    return this.swapAmount / this.exchangeRate;
  }

  requestMVTSwap(): void {
    if (!this.swapAmount || this.swapAmount <= 0) {
      this.toastr.warning('Please enter a valid amount');
      return;
    }

    const mvtAmount = this.calculateMVTAmount();
    if (mvtAmount <= 0) {
      this.toastr.warning('Exchange rate issue - please try again later');
      return;
    }

    this.isRequestingSwap = true;
    this.spinnerMessage = `Requesting swap of ${this.swapAmount} USDC for ${mvtAmount.toFixed(2)} MVT...`;
    this.spinner.show('usdc_deposit_spinner');

    this.sharedService.requestMVTSwap(mvtAmount).subscribe({
      next: (response: any) => {
        if (response?.data?.requestMVTSwap) {
          const swapResponse = response.data.requestMVTSwap;
          if (swapResponse.statusCode === 200) {
            this.toastr.success(swapResponse.message ?? 'Swap request submitted successfully');
            this.swapAmount = 0;
          } else {
            this.toastr.error(swapResponse.message ?? 'Failed to request MVT swap');
          }
        }

        this.isRequestingSwap = false;
        this.spinnerMessage = '';
        this.spinner.hide('usdc_deposit_spinner');
      },
      error: (error: any) => {
        console.error('Error requesting MVT swap:', error);

        this.isRequestingSwap = false;
        this.spinnerMessage = '';
        this.spinner.hide('usdc_deposit_spinner');
        this.toastr.error('Failed to request MVT swap');
      }
    });
  }
}
