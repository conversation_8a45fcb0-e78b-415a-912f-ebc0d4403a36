import { Injectable } from '@angular/core';
import * as AWS from 'aws-sdk';
import { TranscribeClient, StartTranscriptionJobCommand, GetTranscriptionJobCommand } from '@aws-sdk/client-transcribe';
import { Apollo } from 'apollo-angular';
import { firstValueFrom } from 'rxjs';

import { TRANSCRIBE_VIDEO_OR_AUDIO } from '../graphql/posts-graphql.queries';

@Injectable({
  providedIn: 'root'
})
export class TranscribeService {

  private readonly transcribeClient: TranscribeClient;

  constructor(private readonly apollo: Apollo) {
    this.transcribeClient = new TranscribeClient({ region: 'us-east-1' });
  }

  async fetchVideoOrAudioTranscriptFrontend(fileUrl: string): Promise<string> {
    const jobName = `transcribe-job-${Date.now()}`;

    const isMp4 = fileUrl.endsWith('.mp4');
    const isMp3 = fileUrl.endsWith('.mp3');
    const isWav = fileUrl.endsWith('.wav');

    let format;
    if (isMp4) {
      format = 'mp4';
    } else if (isMp3) {
      format = 'mp3';
    } else if (isWav) {
      format = 'wav';
    } else {
      format = null;
    }

    if (!format) {
      throw new Error("Unsupported file format. Only mp4, mp3, and wav are supported.");
    }

    try {
      const startCommand = new StartTranscriptionJobCommand({
        TranscriptionJobName: jobName,
        LanguageCode: "en-US",
        MediaFormat: format,
        Media: {
          MediaFileUri: fileUrl,
        },
        OutputBucketName: "",
      });

      await this.transcribeClient.send(startCommand);

      let status = "IN_PROGRESS";
      let response;
      while (status === "IN_PROGRESS") {
        await new Promise((resolve) => setTimeout(resolve, 5000));
        const getCommand = new GetTranscriptionJobCommand({
          TranscriptionJobName: jobName,
        });
        response = await this.transcribeClient.send(getCommand);
        status = response.TranscriptionJob.TranscriptionJobStatus;
        if (status === "FAILED") {
          throw new Error("Transcription job failed");
        }
      }

      if (status === "COMPLETED" && response) {
        const s3 = new AWS.S3();
        const params = {
          Bucket: process.env.STORAGE_S3MYVILLAGEPROJECTADMINPORTALORGPROFILELOGO_BUCKETNAME,
          Key: response.TranscriptionJob.Transcript.TranscriptFileUri.split('/').slice(4).join('/'),
        };
        const s3Data = await s3.getObject(params).promise();
        // Check if s3Data.Body is a Buffer, which is expected for S3 data
        if (Buffer.isBuffer(s3Data.Body)) {
          const transcript = JSON.parse(s3Data.Body.toString('utf-8'));
          return transcript.results.transcripts[0].transcript;
        } else {
          // Handle the case where Body is not a Buffer, if needed
          throw new Error('Unexpected Body format');
        }


      }
    } catch (error) {
      console.error("Error transcribing file:", error);
      throw error;
    }
  }

  async fetchVideoOrAudioTranscript(s3URI: string, id: string, transcriptionModule: string): Promise<any> {
    
    try {
      const result = await firstValueFrom(
        this.apollo.query({
          query: TRANSCRIBE_VIDEO_OR_AUDIO,
          variables: { s3URI, id, transcriptionModule },
          fetchPolicy: 'network-only' // Ensure we get fresh data
        })
      );
      
      const resultAny = result as any;
      const data = resultAny?.data?.transcribeVideoOrAudio?.data;
      
      if (!data) {
        console.error('No data received from transcription service');
        throw new Error('No data received from transcription service');
      }

      let transcriptText = '';
      let transcriptDetail = null;
      
      if (data.transcriptDetail) {
        try {
          transcriptDetail = typeof data.transcriptDetail === 'string' 
            ? JSON.parse(data.transcriptDetail) 
            : data.transcriptDetail;
          transcriptText = transcriptDetail?.results?.transcripts?.[0]?.transcript ?? '';
        } catch (parseError) {
          console.error('Error parsing transcript detail:', parseError);
          // If parsing fails, try to get text directly
          transcriptText = typeof data.transcriptDetail === 'string' 
            ? data.transcriptDetail 
            : JSON.stringify(data.transcriptDetail);
        }
      } else if (data.transcription) {
        transcriptText = data.transcription;
      }
      
      // Return structured data
      return {
        ...data,
        transcriptText,
        transcriptDetail: transcriptDetail ?? data.transcriptDetail
      };
      
    } catch (error) {
      console.error('Error in fetchVideoOrAudioTranscript:', {
        error,
        s3URI,
        id,
        transcriptionModule
      });
      throw error; // Re-throw to allow handling in the calling function
    }
  }
}
