<div class="card-header border-0">
  <p class="h3 card-title align-items-start flex-column">
    <span class="card-label fw-bold text-dark">
      Recent Community Logs
    </span>
    <span class="text-muted mt-1 fw-semibold fs-7">
      Platform & system actions
    </span>
  </p>
  <div class="card-toolbar">
    <button type="button" class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary"
      [routerLink]="['/logs']" aria-label="All Activities">
      <span class="svg-icon svg-icon-2">
        <app-svg-general svg="gen024"></app-svg-general>
      </span>
    </button>
  </div>
</div>
<div class="card-body pt-2 scroll-y mh-550px">
  <div class="timeline-label" *ngIf="activitiesList.length > 0">
    <div class="timeline-item" *ngFor="let activity of activitiesList; index as i">
      <ng-container
        *ngIf="activity?.type !== 'TOKENS' && activity.moduleType!=='homework-submission' && activity.moduleType!=='update-profile' && activity.moduleType !== 'fundTransactions' && activity.type !== 'GAME'">
        <div class="timeline-label fw-bolder text-gray-800 fs-6">
          {{ activity.createdAt | date: 'h:mm a' }}
        </div>
        <div class="timeline-badge">
          <em class="fa fa-genderless fs-1"
            [ngClass]="{'text-warning': activity.moduleType === 'member', 'text-success': activity.moduleType === 'project', 'text-danger': activity.moduleType === 'project', 'text-info': activity.moduleType === 'post'}"></em>
        </div>
        <div class="timeline-content fw-normal text-gray-800 ps-3">
          <app-activity-content
            [activity]="activity"
            [cityData]="cityData"
            (profileNavigation)="profileNavigation($event.activity, $event.url)"
            (navigateToOrganization)="navigateToOrganization($event)">
          </app-activity-content>
          <app-activity-meta
            [activity]="activity"
            [layout]="'inline'">
          </app-activity-meta>.
        </div>
      </ng-container>
      <ng-container *ngIf="activity?.type !== 'TOKENS' && activity.moduleType==='homework-submission'">
        <div class="timeline-label fw-bolder text-gray-800 fs-6">
          {{ activity.createdAt | date: 'h:mm a' }}
        </div>
        <div class="timeline-badge">
          <em class="fa fa-genderless fs-1"
            [ngClass]="{'text-warning': activity.moduleType === 'member', 'text-success': activity.moduleType === 'project', 'text-danger': activity.moduleType === 'project', 'text-info': activity.moduleType === 'post'}"></em>
        </div>
        <div class="timeline-content fw-normal text-gray-800 ps-3">
          <strong><a class="text-gray-800 text-hover-primary text-break"
              (click)="sharedService.doNavigation(activity,'relatedName')">{{activity?.relatedName
              || '
              '}}</a></strong>
          has
          submitted their homework <strong><a
              [routerLink]="['/activity-submission/view-submission', activity.moduleId]"
              class="text-gray-800 text-hover-primary text-break">{{activity.moduleName}}</a></strong>.
          <br />
          <app-activity-meta
            [activity]="activity"
            [layout]="'block'">
          </app-activity-meta>
        </div>
      </ng-container>
      <ng-container *ngIf="activity?.type !== 'TOKENS' && activity.moduleType==='update-profile'">
        <div class="timeline-label fw-bolder text-gray-800 fs-6">
          {{ activity.createdAt | date: 'h:mm a' }}
        </div>
        <div class="timeline-badge">
          <em class="fa fa-genderless fs-1"
            [ngClass]="{'text-warning': activity.moduleType === 'member', 'text-success': activity.moduleType === 'project', 'text-danger': activity.moduleType === 'project', 'text-info': activity.moduleType === 'post'}"></em>
        </div>
        <div class="timeline-content fw-normal text-gray-800 ps-3">
          <strong><a class="text-gray-800 text-hover-primary text-break"
              (click)="profileNavigation(activity, activity?.moduleUser?.isStakeholder ? '/stakeholders/view-stakeholder/' + activity.moduleId : '/students/view-student/' + activity.moduleId)">{{activity?.moduleName
              || '
              '}}</a></strong>
          has
          requested <strong><a
              (click)="profileNavigation(activity, activity?.moduleUser?.isStakeholder ? '/stakeholders/view-stakeholder/' + activity.moduleId : '/students/view-student/' + activity.moduleId)"
              class="text-gray-800 text-hover-primary text-break">{{activity.relatedName}}</a></strong>.
          <br />
          <app-activity-meta
            [activity]="activity"
            [layout]="'block'">
          </app-activity-meta>
        </div>
      </ng-container>
      <ng-container *ngIf="activity.moduleType==='fundTransactions' && activity?.type!=='TOKENS'">
        <div class="timeline-label fw-bolder text-gray-800 fs-6">
          {{ activity.createdAt | date: 'h:mm a' }}
        </div>
        <div class="timeline-badge">
          <em class="fa fa-genderless fs-1"
            [ngClass]="{'text-warning': activity.moduleType === 'member', 'text-success': activity.moduleType === 'project', 'text-danger': activity.moduleType === 'project', 'text-info': activity.moduleType === 'post'}"></em>
        </div>
        <div class="timeline-content fw-normal text-gray-800 ps-3">
          <strong><a class="text-gray-800 text-hover-primary text-break"
              (click)="sharedService.doNavigation(activity,'relatedName')">{{activity?.moduleName
              || '
              '}}</a></strong>
          imported transactions in <strong><a class="text-gray-800 text-hover-primary text-break">{{
              cityData[activity.cityId] | titlecase }}</a></strong> city.
          <br />
          <app-activity-meta
            [activity]="activity"
            [layout]="'block'">
          </app-activity-meta>
        </div>
      </ng-container>
      <ng-container *ngIf="activity?.type==='GAME'">
        <div class="timeline-label fw-bolder text-gray-800 fs-6">
          {{ activity.createdAt | date: 'h:mm a' }}
        </div>
        <div class="timeline-badge">
          <em class="fa fa-genderless fs-1"
            [ngClass]="{'text-warning': activity.moduleType === 'member', 'text-success': activity.moduleType === 'project', 'text-danger': activity.moduleType === 'project', 'text-info': activity.moduleType === 'post'}"></em>
        </div>
        <div class="timeline-content fw-normal text-gray-800 ps-3">
          <strong>
            <a class="text-gray-800 text-hover-primary">
              {{ activity.moduleName | titlecase }}
            </a>
          </strong>
          has logged into
          <strong> <a class="text-gray-800 text-hover-primary">
              {{ activity?.gameData?.gameName | titlecase }}
            </a></strong>.
          <br />
          <app-activity-meta
            [activity]="{createdUserName: activity.moduleName}"
            [layout]="'block'">
          </app-activity-meta>
        </div>
      </ng-container>
    </div>
  </div>
  <div class="d-flex justify-content-center align-items-center text-gray-700 fw-semibold fs-6"
    *ngIf="activitiesList.length === 0">
    No logs!
  </div>
</div>
<ngx-spinner name="dashboard_activity_spinner" [fullScreen]="false" type="ball-clip-rotate" bdColor="white"
  size="medium" color="#354029"></ngx-spinner>